#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DEMO THUẬT TOÁN FORD-FULKERSON BẰNG PYTHON
Tác giả: [Tên sinh viên]
<PERSON><PERSON> tả: Demo đơn giản thuật toán <PERSON>-Fulkerson để test nhanh
"""

from collections import deque
import sys

class MaxFlowDemo:
    def __init__(self, vertices):
        self.V = vertices
        self.capacity = [[0] * vertices for _ in range(vertices)]
        self.flow = [[0] * vertices for _ in range(vertices)]
    
    def add_edge(self, from_vertex, to_vertex, cap):
        """Thêm cạnh vào đồ thị"""
        self.capacity[from_vertex][to_vertex] = cap
    
    def bfs(self, source, sink, parent):
        """BFS để tìm đường tăng luồng"""
        visited = [False] * self.V
        queue = deque([source])
        visited[source] = True
        parent[source] = -1
        
        while queue:
            u = queue.popleft()
            
            for v in range(self.V):
                # <PERSON><PERSON><PERSON> tra cạnh trong đồ thị dư
                if not visited[v] and self.capacity[u][v] - self.flow[u][v] > 0:
                    if v == sink:
                        parent[v] = u
                        return True
                    queue.append(v)
                    visited[v] = True
                    parent[v] = u
        
        return False
    
    def ford_fulkerson(self, source, sink):
        """Thuật toán Ford-Fulkerson chính"""
        parent = [-1] * self.V
        max_flow = 0
        iteration = 1
        
        print("=" * 50)
        print("    DEMO THUẬT TOÁN FORD-FULKERSON")
        print("=" * 50)
        print(f"Nguồn: {source}, Đích: {sink}")
        print()
        
        while self.bfs(source, sink, parent):
            print(f"--- Lần lặp {iteration} ---")
            
            # Tìm luồng tối thiểu trên đường tăng luồng
            path_flow = float('inf')
            path = []
            
            # Xây dựng đường đi từ sink về source
            v = sink
            while v != source:
                path.append(v)
                u = parent[v]
                path_flow = min(path_flow, self.capacity[u][v] - self.flow[u][v])
                v = u
            path.append(source)
            path.reverse()
            
            # In đường tăng luồng
            print("Đường tăng luồng:", " -> ".join(map(str, path)))
            print(f"Luồng có thể tăng: {path_flow}")
            
            # Cập nhật luồng
            v = sink
            while v != source:
                u = parent[v]
                self.flow[u][v] += path_flow
                self.flow[v][u] -= path_flow
                v = u
            
            max_flow += path_flow
            print(f"Tổng luồng hiện tại: {max_flow}")
            print()
            iteration += 1
        
        print("=" * 50)
        print(f"LUỒNG CỰC ĐẠI: {max_flow}")
        print("=" * 50)
        
        return max_flow
    
    def print_capacity_matrix(self):
        """In ma trận dung lượng"""
        print("\n=== MA TRẬN DUNG LƯỢNG ===")
        print("    ", end="")
        for i in range(self.V):
            print(f"{i:4}", end="")
        print()
        
        for i in range(self.V):
            print(f"{i:2}: ", end="")
            for j in range(self.V):
                print(f"{self.capacity[i][j]:4}", end="")
            print()
        print()
    
    def print_flow_matrix(self):
        """In ma trận luồng kết quả"""
        print("\n=== MA TRẬN LUỒNG KẾT QUẢ ===")
        print("    ", end="")
        for i in range(self.V):
            print(f"{i:4}", end="")
        print()
        
        for i in range(self.V):
            print(f"{i:2}: ", end="")
            for j in range(self.V):
                print(f"{self.flow[i][j]:4}", end="")
            print()
        print()
    
    def print_flow_details(self):
        """In chi tiết luồng trên từng cạnh"""
        print("\n=== CHI TIẾT LUỒNG TRÊN CÁC CẠNH ===")
        print("Cạnh\t\tLuồng/Dung lượng\tTỷ lệ sử dụng")
        print("-" * 50)
        
        for i in range(self.V):
            for j in range(self.V):
                if self.capacity[i][j] > 0:
                    utilization = (self.flow[i][j] / self.capacity[i][j]) * 100
                    print(f"({i},{j})\t\t{self.flow[i][j]}/{self.capacity[i][j]}\t\t{utilization:.1f}%")
        print()

def demo_test_case_1():
    """Test case 1: Mạng đơn giản 4 đỉnh"""
    print("🎯 TEST CASE 1: MẠNG LUỒNG ĐƠN GIẢN (4 ĐỈNH)")
    print("=" * 60)
    
    print("Cấu trúc mạng:")
    print("s(0) --16--> 1 --12--> t(3)")
    print(" |           |          ^")
    print(" 13         10         14")
    print(" |           v          |")
    print(" +-----> 2 --4--> 1 ----+")
    print("         |              ")
    print("         +------14------>")
    
    graph = MaxFlowDemo(4)
    
    # Thêm các cạnh
    graph.add_edge(0, 1, 16)  # s -> 1
    graph.add_edge(0, 2, 13)  # s -> 2
    graph.add_edge(1, 2, 10)  # 1 -> 2
    graph.add_edge(1, 3, 12)  # 1 -> t
    graph.add_edge(2, 1, 4)   # 2 -> 1
    graph.add_edge(2, 3, 14)  # 2 -> t
    
    graph.print_capacity_matrix()
    
    # Tìm luồng cực đại
    source, sink = 0, 3
    max_flow = graph.ford_fulkerson(source, sink)
    
    graph.print_flow_matrix()
    graph.print_flow_details()
    
    return max_flow

def demo_test_case_2():
    """Test case 2: Mạng phức tạp 6 đỉnh"""
    print("\n🎯 TEST CASE 2: MẠNG LUỒNG PHỨC TẠP (6 ĐỈNH)")
    print("=" * 60)
    
    print("Cấu trúc mạng:")
    print("     1 --4--> 3")
    print("   / |        | \\")
    print("  10 2        6  10")
    print(" /   |        |   \\")
    print("s    v        v    t")
    print(" \\   4 --6--> 4   /")
    print("  10 |        |  10")
    print("   \\ v        | /")
    print("     2 --9----+")
    
    graph = MaxFlowDemo(6)
    
    # Thêm các cạnh
    graph.add_edge(0, 1, 10)  # s -> 1
    graph.add_edge(0, 2, 10)  # s -> 2
    graph.add_edge(1, 2, 2)   # 1 -> 2
    graph.add_edge(1, 3, 4)   # 1 -> 3
    graph.add_edge(1, 4, 8)   # 1 -> 4
    graph.add_edge(2, 4, 9)   # 2 -> 4
    graph.add_edge(3, 5, 10)  # 3 -> t
    graph.add_edge(4, 3, 6)   # 4 -> 3
    graph.add_edge(4, 5, 10)  # 4 -> t
    
    graph.print_capacity_matrix()
    
    # Tìm luồng cực đại
    source, sink = 0, 5
    max_flow = graph.ford_fulkerson(source, sink)
    
    graph.print_flow_matrix()
    graph.print_flow_details()
    
    return max_flow

def main():
    """Hàm main"""
    print("🚀 CHƯƠNG TRÌNH DEMO THUẬT TOÁN FORD-FULKERSON")
    print("Tác giả: [Tên sinh viên]")
    print("Ngôn ngữ: Python")
    print("Môn học: Cấu trúc dữ liệu và Giải thuật")
    print()
    
    try:
        # Chạy test case 1
        max_flow_1 = demo_test_case_1()
        
        print("\n" + "="*60)
        input("Nhấn Enter để tiếp tục test case 2...")
        
        # Chạy test case 2
        max_flow_2 = demo_test_case_2()
        
        # Tổng kết
        print("\n🎉 TỔNG KẾT KẾT QUẢ:")
        print("=" * 40)
        print(f"Test case 1 (4 đỉnh): Luồng cực đại = {max_flow_1}")
        print(f"Test case 2 (6 đỉnh): Luồng cực đại = {max_flow_2}")
        print()
        print("✅ Thuật toán Ford-Fulkerson hoạt động chính xác!")
        print("✅ Kết quả phù hợp với định lý Max-Flow Min-Cut!")
        
    except KeyboardInterrupt:
        print("\n\nChương trình bị dừng bởi người dùng.")
    except Exception as e:
        print(f"\nLỗi: {e}")
    
    print("\nCảm ơn bạn đã sử dụng chương trình demo!")

if __name__ == "__main__":
    main()
