@model CoffeeShopWebsite.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "Đăng nhập";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-5">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4><i class="fas fa-sign-in-alt me-2"></i><PERSON><PERSON>ng nhập</h4>
                    <p class="mb-0">Chào mừng bạn quay trở lại!</p>
                </div>
                <div class="card-body">
                    <form asp-action="Login" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <input type="hidden" asp-for="@ViewData["ReturnUrl"]" />
                        
                        <div class="mb-3">
                            <label asp-for="Email" class="form-label"></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input asp-for="Email" class="form-control" placeholder="Nhập email" autofocus />
                            </div>
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Password" class="form-label"></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input asp-for="Password" class="form-control" placeholder="Nhập mật khẩu" />
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="mb-3 form-check">
                            <input asp-for="RememberMe" class="form-check-input" />
                            <label asp-for="RememberMe" class="form-check-label">
                                Ghi nhớ đăng nhập
                            </label>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập
                            </button>
                        </div>
                    </form>

                    <div class="text-center mt-3">
                        <p>
                            <a href="#" class="text-decoration-none">Quên mật khẩu?</a>
                        </p>
                        <p>Chưa có tài khoản? <a asp-action="Register">Đăng ký ngay</a></p>
                    </div>

                    <!-- Quick Login Info -->
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6><i class="fas fa-info-circle me-2 text-info"></i>Đăng nhập để:</h6>
                        <ul class="list-unstyled mb-0">
                            <li><i class="fas fa-check text-success me-2"></i>Theo dõi đơn hàng</li>
                            <li><i class="fas fa-check text-success me-2"></i>Sử dụng mã giảm giá</li>
                            <li><i class="fas fa-check text-success me-2"></i>Tích điểm thưởng</li>
                            <li><i class="fas fa-check text-success me-2"></i>Lưu địa chỉ giao hàng</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Promotion Banner -->
        <div class="col-md-4 ms-3">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark text-center">
                    <h5><i class="fas fa-gift me-2"></i>Ưu đãi đặc biệt</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-percentage fa-3x text-warning"></i>
                    </div>
                    <h6 class="text-center">Thành viên mới</h6>
                    <p class="text-center">Giảm <strong>10%</strong> đơn hàng đầu tiên</p>
                    <p class="text-center">+ <strong>100 điểm</strong> thưởng</p>
                    
                    <hr>
                    
                    <h6 class="text-center">Thành viên Gold</h6>
                    <p class="text-center">Giảm <strong>20%</strong> mọi đơn hàng</p>
                    <p class="text-center">Miễn phí vận chuyển</p>
                    
                    <div class="text-center mt-3">
                        <a asp-action="Register" class="btn btn-warning">
                            <i class="fas fa-user-plus me-1"></i>Đăng ký ngay
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            // Toggle password visibility
            $('#togglePassword').click(function() {
                var passwordField = $('#Password');
                var icon = $(this).find('i');
                
                if (passwordField.attr('type') === 'password') {
                    passwordField.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    passwordField.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });
        });
    </script>
}
