#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DEMO TƯƠNG TÁC THUẬT TOÁN FORD-FULKERSON
Tác giả: [Tên sinh viên]
Mô tả: Chương trình tương tác cho phép người dùng nhập mạng tùy chỉnh
"""

from collections import deque

class InteractiveMaxFlow:
    def __init__(self, vertices):
        self.V = vertices
        self.capacity = [[0] * vertices for _ in range(vertices)]
        self.flow = [[0] * vertices for _ in range(vertices)]
        self.original_capacity = [[0] * vertices for _ in range(vertices)]
    
    def add_edge(self, from_vertex, to_vertex, cap):
        """Thêm cạnh vào đồ thị"""
        self.capacity[from_vertex][to_vertex] = cap
        self.original_capacity[from_vertex][to_vertex] = cap
    
    def reset_flow(self):
        """Reset luồng về 0"""
        self.flow = [[0] * self.V for _ in range(self.V)]
        for i in range(self.V):
            for j in range(self.V):
                self.capacity[i][j] = self.original_capacity[i][j]
    
    def bfs(self, source, sink, parent):
        """BFS để tìm đường tăng luồng"""
        visited = [False] * self.V
        queue = deque([source])
        visited[source] = True
        parent[source] = -1
        
        while queue:
            u = queue.popleft()
            
            for v in range(self.V):
                if not visited[v] and self.capacity[u][v] - self.flow[u][v] > 0:
                    if v == sink:
                        parent[v] = u
                        return True
                    queue.append(v)
                    visited[v] = True
                    parent[v] = u
        
        return False
    
    def ford_fulkerson(self, source, sink, show_steps=True):
        """Thuật toán Ford-Fulkerson với tùy chọn hiển thị bước"""
        parent = [-1] * self.V
        max_flow = 0
        iteration = 1
        
        if show_steps:
            print(f"\n🚀 BẮT ĐẦU THUẬT TOÁN FORD-FULKERSON")
            print(f"Nguồn: {source}, Đích: {sink}")
            print("=" * 50)
        
        while self.bfs(source, sink, parent):
            if show_steps:
                print(f"\n--- Lần lặp {iteration} ---")
            
            # Tìm luồng tối thiểu
            path_flow = float('inf')
            path = []
            
            v = sink
            while v != source:
                path.append(v)
                u = parent[v]
                path_flow = min(path_flow, self.capacity[u][v] - self.flow[u][v])
                v = u
            path.append(source)
            path.reverse()
            
            if show_steps:
                print(f"Đường tăng luồng: {' -> '.join(map(str, path))}")
                print(f"Luồng có thể tăng: {path_flow}")
            
            # Cập nhật luồng
            v = sink
            while v != source:
                u = parent[v]
                self.flow[u][v] += path_flow
                self.flow[v][u] -= path_flow
                v = u
            
            max_flow += path_flow
            if show_steps:
                print(f"Tổng luồng hiện tại: {max_flow}")
            
            iteration += 1
        
        if show_steps:
            print("=" * 50)
            print(f"🎯 LUỒNG CỰC ĐẠI: {max_flow}")
            print("=" * 50)
        
        return max_flow
    
    def print_network_info(self):
        """In thông tin mạng"""
        print(f"\n📊 THÔNG TIN MẠNG LUỒNG ({self.V} đỉnh)")
        print("=" * 40)
        
        edge_count = 0
        total_capacity = 0
        
        print("Danh sách các cạnh:")
        for i in range(self.V):
            for j in range(self.V):
                if self.original_capacity[i][j] > 0:
                    print(f"  {i} -> {j}: dung lượng {self.original_capacity[i][j]}")
                    edge_count += 1
                    total_capacity += self.original_capacity[i][j]
        
        print(f"\nTổng số cạnh: {edge_count}")
        print(f"Tổng dung lượng: {total_capacity}")
    
    def print_results(self, max_flow):
        """In kết quả chi tiết"""
        print(f"\n📈 KẾT QUẢ CHI TIẾT")
        print("=" * 40)
        
        print("Luồng trên các cạnh:")
        for i in range(self.V):
            for j in range(self.V):
                if self.original_capacity[i][j] > 0:
                    utilization = (self.flow[i][j] / self.original_capacity[i][j]) * 100
                    status = "🔴" if utilization == 100 else "🟡" if utilization > 50 else "🟢"
                    print(f"  {status} Cạnh ({i},{j}): {self.flow[i][j]}/{self.original_capacity[i][j]} ({utilization:.1f}%)")
        
        print(f"\n🏆 Luồng cực đại: {max_flow}")

def display_menu():
    """Hiển thị menu chính"""
    print("\n" + "="*50)
    print("    🌊 DEMO TƯƠNG TÁC THUẬT TOÁN FORD-FULKERSON")
    print("="*50)
    print("1. 📝 Nhập mạng luồng mới")
    print("2. 🧪 Test case mẫu 1 (4 đỉnh)")
    print("3. 🧪 Test case mẫu 2 (6 đỉnh)")
    print("4. 📚 Hướng dẫn sử dụng")
    print("0. 🚪 Thoát")
    print("-" * 50)

def input_custom_network():
    """Nhập mạng tùy chỉnh"""
    print("\n📝 NHẬP MẠNG LUỒNG TÙY CHỈNH")
    print("=" * 40)
    
    try:
        vertices = int(input("Nhập số đỉnh (≥ 2): "))
        if vertices < 2:
            print("❌ Số đỉnh phải ≥ 2!")
            return
        
        graph = InteractiveMaxFlow(vertices)
        
        edges = int(input("Nhập số cạnh: "))
        print(f"\nNhập thông tin {edges} cạnh (đỉnh từ 0 đến {vertices-1}):")
        
        for i in range(edges):
            print(f"Cạnh {i+1}:")
            from_v = int(input("  Từ đỉnh: "))
            to_v = int(input("  Đến đỉnh: "))
            capacity = int(input("  Dung lượng: "))
            
            if 0 <= from_v < vertices and 0 <= to_v < vertices and capacity > 0:
                graph.add_edge(from_v, to_v, capacity)
            else:
                print("  ❌ Thông tin không hợp lệ!")
                return
        
        source = int(input(f"\nNhập đỉnh nguồn (0-{vertices-1}): "))
        sink = int(input(f"Nhập đỉnh đích (0-{vertices-1}): "))
        
        if not (0 <= source < vertices and 0 <= sink < vertices and source != sink):
            print("❌ Đỉnh nguồn/đích không hợp lệ!")
            return
        
        # Hiển thị thông tin và chạy thuật toán
        graph.print_network_info()
        
        show_steps = input("\nHiển thị từng bước? (y/n): ").lower() == 'y'
        max_flow = graph.ford_fulkerson(source, sink, show_steps)
        
        graph.print_results(max_flow)
        
    except ValueError:
        print("❌ Vui lòng nhập số nguyên hợp lệ!")
    except Exception as e:
        print(f"❌ Lỗi: {e}")

def run_sample_test(test_num):
    """Chạy test case mẫu"""
    if test_num == 1:
        print("\n🧪 TEST CASE MẪU 1: MẠNG 4 ĐỈNH")
        graph = InteractiveMaxFlow(4)
        graph.add_edge(0, 1, 16)
        graph.add_edge(0, 2, 13)
        graph.add_edge(1, 2, 10)
        graph.add_edge(1, 3, 12)
        graph.add_edge(2, 1, 4)
        graph.add_edge(2, 3, 14)
        source, sink = 0, 3
        
    elif test_num == 2:
        print("\n🧪 TEST CASE MẪU 2: MẠNG 6 ĐỈNH")
        graph = InteractiveMaxFlow(6)
        graph.add_edge(0, 1, 10)
        graph.add_edge(0, 2, 10)
        graph.add_edge(1, 2, 2)
        graph.add_edge(1, 3, 4)
        graph.add_edge(1, 4, 8)
        graph.add_edge(2, 4, 9)
        graph.add_edge(3, 5, 10)
        graph.add_edge(4, 3, 6)
        graph.add_edge(4, 5, 10)
        source, sink = 0, 5
    
    graph.print_network_info()
    show_steps = input("\nHiển thị từng bước? (y/n): ").lower() == 'y'
    max_flow = graph.ford_fulkerson(source, sink, show_steps)
    graph.print_results(max_flow)

def show_instructions():
    """Hiển thị hướng dẫn"""
    print("\n📚 HƯỚNG DẪN SỬ DỤNG")
    print("=" * 40)
    print("1. 🎯 MỤC ĐÍCH:")
    print("   - Demo thuật toán Ford-Fulkerson tìm luồng cực đại")
    print("   - Minh họa định lý Max-Flow Min-Cut")
    
    print("\n2. 📝 CÁCH NHẬP MẠNG:")
    print("   - Đỉnh được đánh số từ 0")
    print("   - Dung lượng phải > 0")
    print("   - Đỉnh nguồn ≠ đỉnh đích")
    
    print("\n3. 📊 KẾT QUẢ HIỂN THỊ:")
    print("   - 🟢 Cạnh sử dụng < 50% dung lượng")
    print("   - 🟡 Cạnh sử dụng 50-99% dung lượng") 
    print("   - 🔴 Cạnh sử dụng 100% dung lượng (bão hòa)")
    
    print("\n4. 💡 MẸO:")
    print("   - Bắt đầu với test case mẫu để hiểu thuật toán")
    print("   - Chọn 'y' để xem từng bước thực hiện")
    print("   - Thử các mạng khác nhau để thấy sự khác biệt")

def main():
    """Hàm main"""
    print("🌊 CHÀO MỪNG ĐẾN VỚI DEMO TƯƠNG TÁC FORD-FULKERSON!")
    print("Tác giả: [Tên sinh viên]")
    print("Môn học: Cấu trúc dữ liệu và Giải thuật")
    
    while True:
        try:
            display_menu()
            choice = input("Lựa chọn của bạn: ").strip()
            
            if choice == '1':
                input_custom_network()
            elif choice == '2':
                run_sample_test(1)
            elif choice == '3':
                run_sample_test(2)
            elif choice == '4':
                show_instructions()
            elif choice == '0':
                print("\n👋 Cảm ơn bạn đã sử dụng chương trình!")
                print("🎓 Chúc bạn học tập tốt!")
                break
            else:
                print("❌ Lựa chọn không hợp lệ!")
            
            if choice != '0':
                input("\n⏸️  Nhấn Enter để tiếp tục...")
                
        except KeyboardInterrupt:
            print("\n\n👋 Chương trình bị dừng. Tạm biệt!")
            break
        except Exception as e:
            print(f"❌ Lỗi không mong muốn: {e}")

if __name__ == "__main__":
    main()
