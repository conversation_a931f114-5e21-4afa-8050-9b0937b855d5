using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using CoffeeShopWebsite.Data;
using CoffeeShopWebsite.Models;
using CoffeeShopWebsite.ViewModels;
using Microsoft.AspNetCore.Identity;

namespace CoffeeShopWebsite.Controllers
{
    [Authorize]
    public class OrderController : Controller
    {
        private readonly CoffeeShopContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public OrderController(CoffeeShopContext context, UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        // GET: Order History
        public async Task<IActionResult> Index(int page = 1, int pageSize = 10)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var query = _context.Orders
                .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
                .Where(o => o.UserId == user.Id)
                .OrderByDescending(o => o.OrderDate);

            var totalItems = await query.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalItems / pageSize);

            var orders = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var viewModel = new OrderListViewModel
            {
                Orders = orders,
                CurrentPage = page,
                TotalPages = totalPages,
                PageSize = pageSize,
                TotalItems = totalItems
            };

            return View(viewModel);
        }

        // GET: Order Details
        public async Task<IActionResult> Details(int id)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var order = await _context.Orders
                .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
                .ThenInclude(p => p.Category)
                .Include(o => o.Coupon)
                .FirstOrDefaultAsync(o => o.Id == id && o.UserId == user.Id);

            if (order == null)
            {
                return NotFound();
            }

            return View(order);
        }

        // POST: Cancel Order
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Cancel(int id)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var order = await _context.Orders
                .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
                .FirstOrDefaultAsync(o => o.Id == id && o.UserId == user.Id);

            if (order == null)
            {
                return NotFound();
            }

            // Only allow cancellation for pending orders
            if (order.Status != OrderStatus.Pending)
            {
                TempData["ErrorMessage"] = "Chỉ có thể hủy đơn hàng đang chờ xử lý";
                return RedirectToAction("Details", new { id });
            }

            // Update order status
            order.Status = OrderStatus.Cancelled;

            // Restore product stock
            foreach (var item in order.OrderItems)
            {
                item.Product.StockQuantity += item.Quantity;
            }

            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "Đã hủy đơn hàng thành công";
            return RedirectToAction("Details", new { id });
        }

        // GET: Reorder
        public async Task<IActionResult> Reorder(int id)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var order = await _context.Orders
                .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
                .FirstOrDefaultAsync(o => o.Id == id && o.UserId == user.Id);

            if (order == null)
            {
                return NotFound();
            }

            // Add items to cart
            string sessionId = HttpContext.Session.Id;
            int addedItems = 0;

            foreach (var orderItem in order.OrderItems)
            {
                if (orderItem.Product.IsActive && orderItem.Product.StockQuantity > 0)
                {
                    var existingCartItem = await _context.CartItems
                        .FirstOrDefaultAsync(c => c.ProductId == orderItem.ProductId && 
                                                 (c.SessionId == sessionId || c.UserId == user.Id));

                    if (existingCartItem != null)
                    {
                        existingCartItem.Quantity += orderItem.Quantity;
                    }
                    else
                    {
                        var cartItem = new CartItem
                        {
                            ProductId = orderItem.ProductId,
                            Quantity = Math.Min(orderItem.Quantity, orderItem.Product.StockQuantity),
                            SessionId = sessionId,
                            UserId = user.Id,
                            AddedAt = DateTime.Now
                        };
                        _context.CartItems.Add(cartItem);
                    }
                    addedItems++;
                }
            }

            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = $"Đã thêm {addedItems} sản phẩm vào giỏ hàng";
            return RedirectToAction("Index", "Cart");
        }

        // GET: Track Order (for guests)
        [AllowAnonymous]
        public IActionResult Track()
        {
            return View();
        }

        // POST: Track Order
        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Track(string orderNumber, string email)
        {
            if (string.IsNullOrEmpty(orderNumber) || string.IsNullOrEmpty(email))
            {
                ModelState.AddModelError("", "Vui lòng nhập đầy đủ thông tin");
                return View();
            }

            var order = await _context.Orders
                .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
                .FirstOrDefaultAsync(o => o.OrderNumber == orderNumber && o.CustomerEmail == email);

            if (order == null)
            {
                ModelState.AddModelError("", "Không tìm thấy đơn hàng với thông tin đã nhập");
                return View();
            }

            return View("TrackResult", order);
        }
    }
}
