using CoffeeShopWebsite.Models;

namespace CoffeeShopWebsite.ViewModels
{
    public class ProductListViewModel
    {
        public IEnumerable<Product> Products { get; set; } = new List<Product>();
        public IEnumerable<Category> Categories { get; set; } = new List<Category>();
        
        // Pagination
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; }
        public int PageSize { get; set; } = 12;
        public int TotalItems { get; set; }
        
        // Filters
        public int? CategoryId { get; set; }
        public string? SearchString { get; set; }
        public string? SortOrder { get; set; }
        public string? SortBy { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }

        // Display info
        public string? CategoryName { get; set; }
        public string? CategoryDescription { get; set; }
        public string? PageTitle { get; set; }
        public Category? Category { get; set; }
        public int TotalCount { get; set; }
        
        // Pagination helpers
        public bool HasPreviousPage => CurrentPage > 1;
        public bool HasNextPage => CurrentPage < TotalPages;
        public int StartItem => (CurrentPage - 1) * PageSize + 1;
        public int EndItem => Math.Min(CurrentPage * PageSize, TotalItems);
        
        // Price range for filters
        public decimal MinPriceRange { get; set; }
        public decimal MaxPriceRange { get; set; }
    }
}
