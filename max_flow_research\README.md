# Nghiên cứu bài toán luồng cực đại trên đồ thị có trọng số và cài đặt minh họa

## 📋 Thông tin dự án

**Đề tài:** Nghiên cứu bài toán luồng cực đại trên đồ thị có trọng số và cài đặt minh họa  
**Sinh viên thực hiện:** [Tên sinh viên]  
**Mã số sinh viên:** [MSSV]  
**Lớp:** [Tên lớp]  
**Giảng viên hướng dẫn:** [Tên giảng viên]  
**Môn học:** Cấu trúc dữ liệu và Giải thuật  
**Ngôn ngữ:** C++

## 🎯 Mục tiêu

- Nghiên cứu lý thuyết về bài toán luồng cực đại (Maximum Flow Problem)
- Tìm hiểu định lý Ford-<PERSON><PERSON><PERSON> và thuật toán liên quan
- Cài đặt thuật to<PERSON>-<PERSON><PERSON>erson bằng C++
- <PERSON><PERSON> tích độ phức tạp và hiệu quả của thuật toán
- Minh họa kết quả trên các test case cụ thể

## 📁 Cấu trúc dự án

```
max_flow_research/
├── docs/                   # Tài liệu báo cáo
│   ├── report.md          # Báo cáo nghiên cứu chính
│   └── references.md      # Tài liệu tham khảo
├── src/                   # Mã nguồn C++
│   ├── graph.h            # Header file định nghĩa lớp Graph
│   ├── max_flow.cpp       # Cài đặt thuật toán Ford-Fulkerson
│   └── main.cpp           # Chương trình chính với menu
├── examples/              # Ví dụ và test cases
│   └── test_cases.txt     # Mô tả các test case
├── build/                 # Thư mục build (tự động tạo)
├── bin/                   # File thực thi (tự động tạo)
├── Makefile              # File build automation
└── README.md             # File này
```

## 🚀 Hướng dẫn sử dụng

### Yêu cầu hệ thống
- **Compiler:** g++ hỗ trợ C++11 trở lên
- **Hệ điều hành:** Windows, Linux, macOS
- **RAM:** Tối thiểu 512MB
- **Dung lượng:** ~10MB

### Biên dịch và chạy

#### Cách 1: Sử dụng Makefile (Khuyến nghị)
```bash
# Biên dịch
make

# Biên dịch và chạy
make run

# Biên dịch với debug
make debug

# Dọn dẹp
make clean

# Xem trợ giúp
make help
```

#### Cách 2: Biên dịch thủ công
```bash
# Tạo thư mục
mkdir -p build bin

# Biên dịch
g++ -std=c++11 -Wall -O2 src/*.cpp -o bin/max_flow

# Chạy
./bin/max_flow
```

#### Cách 3: Windows với Dev-C++ hoặc Visual Studio
1. Mở tất cả file .cpp và .h trong IDE
2. Build project
3. Chạy file thực thi

## 🎮 Chức năng chương trình

### Menu chính
1. **Test case 1** - Mạng đơn giản (4 đỉnh)
2. **Test case 2** - Mạng phức tạp (6 đỉnh)  
3. **Test case 3** - Mạng tùy chỉnh (5 đỉnh)
4. **Nhập mạng luồng mới** - Tự định nghĩa mạng
5. **Hướng dẫn sử dụng** - Trợ giúp chi tiết

### Kết quả hiển thị
- **Ma trận dung lượng:** Hiển thị cấu trúc mạng ban đầu
- **Quá trình tìm kiếm:** Từng bước tìm đường tăng luồng
- **Ma trận luồng:** Kết quả luồng cuối cùng
- **Chi tiết cạnh:** Luồng và tỷ lệ sử dụng từng cạnh
- **Lát cắt tối thiểu:** Phân tích lát cắt và phân chia đỉnh

## 🧪 Test Cases

### Test Case 1: Mạng đơn giản
- **Đỉnh:** 4 (s=0, t=3)
- **Luồng cực đại:** 23
- **Mục đích:** Minh họa khái niệm cơ bản

### Test Case 2: Mạng phức tạp  
- **Đỉnh:** 6 (s=0, t=5)
- **Luồng cực đại:** 19
- **Mục đích:** Test với nhiều đường đi

### Test Case 3: Đường đi song song
- **Đỉnh:** 5 (s=0, t=4)  
- **Luồng cực đại:** 50
- **Mục đích:** Test tối ưu hóa đường đi

## 🔬 Thuật toán

### Ford-Fulkerson Algorithm
```cpp
1. Khởi tạo luồng f = 0 cho mọi cạnh
2. Xây dựng đồ thị dư Gf từ G và f
3. While tồn tại đường đi từ s đến t trong Gf:
   a. Tìm đường tăng luồng P bằng BFS
   b. Tính dung lượng tối thiểu cf(P) trên P  
   c. Cập nhật luồng f dọc theo P
   d. Cập nhật đồ thị dư Gf
4. Return f (luồng cực đại)
```

### Độ phức tạp
- **Thời gian:** O(E × |f*|) với |f*| là luồng cực đại
- **Không gian:** O(V²) cho ma trận kề
- **Cải tiến Edmonds-Karp:** O(VE²) với BFS

## 📊 Kết quả nghiên cứu

### Ưu điểm
- ✅ Thuật toán đơn giản, dễ hiểu
- ✅ Đảm bảo tìm được luồng cực đại  
- ✅ Cài đặt ổn định và chính xác
- ✅ Minh họa rõ ràng từng bước thực hiện

### Hạn chế
- ⚠️ Độ phức tạp có thể cao với đồ thị lớn
- ⚠️ Sử dụng ma trận kề tốn bộ nhớ
- ⚠️ Chưa tối ưu cho đồ thị thưa

### Ứng dụng thực tế
- 🚗 Tối ưu hóa giao thông
- 🌐 Quản lý băng thông mạng
- 🏭 Phân bổ tài nguyên sản xuất
- 📦 Tối ưu chuỗi cung ứng

## 📚 Tài liệu tham khảo

1. **Cormen et al.** - *Introduction to Algorithms* (3rd Edition)
2. **Kleinberg & Tardos** - *Algorithm Design*  
3. **Ford & Fulkerson** - *Maximal flow through a network* (1956)
4. **Edmonds & Karp** - *Theoretical improvements in algorithmic efficiency* (1972)

## 👨‍💻 Tác giả

**[Tên sinh viên]**  
📧 Email: [<EMAIL>]  
🎓 Trường: [Tên trường]  
📅 Năm: [Năm học]

## 📄 License

Dự án này được phát triển cho mục đích học tập và nghiên cứu.

---

*"Thuật toán Ford-Fulkerson không chỉ giải quyết bài toán luồng cực đại mà còn mở ra cánh cửa cho nhiều ứng dụng tối ưu hóa khác trong thực tế."*
