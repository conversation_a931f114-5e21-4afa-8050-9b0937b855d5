@model CoffeeShopWebsite.Models.Product

@{
    ViewData["Title"] = Model.Name;
}

<div class="container mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
            <li class="breadcrumb-item"><a asp-controller="Products" asp-action="Index">Sản phẩm</a></li>
            <li class="breadcrumb-item"><a asp-controller="Products" asp-action="Category" asp-route-id="@Model.CategoryId">@Model.Category.Name</a></li>
            <li class="breadcrumb-item active" aria-current="page">@Model.Name</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Product Image -->
        <div class="col-md-6 mb-4">
            <div class="product-image-container">
                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                {
                    <img src="@Model.ImageUrl" class="img-fluid rounded shadow" alt="@Model.Name" style="width: 100%; max-height: 500px; object-fit: cover;">
                }
                else
                {
                    <div class="bg-light d-flex align-items-center justify-content-center rounded shadow" style="height: 500px;">
                        <i class="fas fa-coffee fa-5x text-muted"></i>
                    </div>
                }
            </div>
        </div>

        <!-- Product Info -->
        <div class="col-md-6">
            <div class="product-info">
                <!-- Product Title -->
                <h1 class="display-5 fw-bold mb-3">@Model.Name</h1>
                
                <!-- Category Badge -->
                <div class="mb-3">
                    <span class="badge bg-primary fs-6">
                        <i class="fas fa-tag me-1"></i>@Model.Category.Name
                    </span>
                    @if (Model.IsFeatured)
                    {
                        <span class="badge bg-warning text-dark fs-6 ms-2">
                            <i class="fas fa-star me-1"></i>Nổi bật
                        </span>
                    }
                </div>

                <!-- Price -->
                <div class="price-section mb-4">
                    <h2 class="text-primary fw-bold">@Model.Price.ToString("N0") VNĐ</h2>
                </div>

                <!-- Description -->
                @if (!string.IsNullOrEmpty(Model.Description))
                {
                    <div class="description-section mb-4">
                        <h5>Mô tả sản phẩm</h5>
                        <p class="text-muted">@Model.Description</p>
                    </div>
                }

                <!-- Stock Status -->
                <div class="stock-section mb-4">
                    <h6>Tình trạng kho:</h6>
                    @if (Model.StockQuantity > 10)
                    {
                        <span class="badge bg-success fs-6">
                            <i class="fas fa-check-circle me-1"></i>Còn hàng (@Model.StockQuantity sản phẩm)
                        </span>
                    }
                    else if (Model.StockQuantity > 0)
                    {
                        <span class="badge bg-warning text-dark fs-6">
                            <i class="fas fa-exclamation-triangle me-1"></i>Sắp hết hàng (@Model.StockQuantity sản phẩm)
                        </span>
                    }
                    else
                    {
                        <span class="badge bg-danger fs-6">
                            <i class="fas fa-times-circle me-1"></i>Hết hàng
                        </span>
                    }
                </div>

                <!-- Add to Cart Form -->
                <div class="add-to-cart-section">
                    @if (Model.StockQuantity > 0)
                    {
                        <div class="row g-3 align-items-end">
                            <div class="col-md-4">
                                <label for="quantity" class="form-label">Số lượng:</label>
                                <input type="number" id="quantity" class="form-control" value="1" min="1" max="@Model.StockQuantity">
                            </div>
                            <div class="col-md-8">
                                <button class="btn btn-primary btn-lg w-100" onclick="addToCart(@Model.Id)">
                                    <i class="fas fa-cart-plus me-2"></i>Thêm vào giỏ hàng
                                </button>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-success btn-lg w-100" onclick="buyNow(@Model.Id)">
                                <i class="fas fa-bolt me-2"></i>Mua ngay
                            </button>
                        </div>
                    }
                    else
                    {
                        <button class="btn btn-secondary btn-lg w-100" disabled>
                            <i class="fas fa-times me-2"></i>Sản phẩm đã hết hàng
                        </button>
                    }
                </div>

                <!-- Product Features -->
                <div class="features-section mt-4">
                    <h6>Đặc điểm nổi bật:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Chất lượng cao, được chọn lọc kỹ càng</li>
                        <li><i class="fas fa-check text-success me-2"></i>Giao hàng nhanh chóng trong ngày</li>
                        <li><i class="fas fa-check text-success me-2"></i>Đảm bảo độ tươi ngon</li>
                        <li><i class="fas fa-check text-success me-2"></i>Hỗ trợ khách hàng 24/7</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Products -->
    <div class="related-products mt-5">
        <h3 class="mb-4">Sản phẩm liên quan</h3>
        <div class="row" id="related-products">
            <!-- Related products will be loaded here via AJAX -->
        </div>
    </div>
</div>

<style>
    .product-image-container img {
        transition: transform 0.3s ease;
    }
    
    .product-image-container:hover img {
        transform: scale(1.05);
    }
    
    .price-section h2 {
        font-size: 2.5rem;
    }
    
    .badge {
        font-size: 0.9rem !important;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1.1rem;
    }
</style>

<script>
    function addToCart(productId) {
        const quantity = document.getElementById('quantity').value;
        
        $.post('@Url.Action("AddToCart", "Cart")', { 
            productId: productId, 
            quantity: quantity 
        })
        .done(function(response) {
            if (response.success) {
                // Update cart count if exists
                if (typeof updateCartCount === 'function') {
                    updateCartCount();
                }
                
                showAlert('success', `Đã thêm ${quantity} sản phẩm vào giỏ hàng!`);
            } else {
                showAlert('error', response.message || 'Có lỗi xảy ra!');
            }
        })
        .fail(function() {
            showAlert('error', 'Có lỗi xảy ra khi thêm sản phẩm!');
        });
    }
    
    function buyNow(productId) {
        const quantity = document.getElementById('quantity').value;
        
        // Add to cart first, then redirect to checkout
        $.post('@Url.Action("AddToCart", "Cart")', { 
            productId: productId, 
            quantity: quantity 
        })
        .done(function(response) {
            if (response.success) {
                // Redirect to cart/checkout
                window.location.href = '@Url.Action("Index", "Cart")';
            } else {
                showAlert('error', response.message || 'Có lỗi xảy ra!');
            }
        })
        .fail(function() {
            showAlert('error', 'Có lỗi xảy ra khi thêm sản phẩm!');
        });
    }
    
    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                ${message}
            </div>
        `;
        
        $('body').append(alertHtml);
        
        // Auto remove after 3 seconds
        setTimeout(function() {
            $('.alert').alert('close');
        }, 3000);
    }
    
    // Load related products
    $(document).ready(function() {
        loadRelatedProducts(@Model.CategoryId, @Model.Id);
    });
    
    function loadRelatedProducts(categoryId, currentProductId) {
        $.get('@Url.Action("GetRelatedProducts", "Products")', { 
            categoryId: categoryId, 
            currentProductId: currentProductId,
            count: 4
        })
        .done(function(data) {
            $('#related-products').html(data);
        })
        .fail(function() {
            $('#related-products').html('<p class="text-muted">Không thể tải sản phẩm liên quan.</p>');
        });
    }
</script>
