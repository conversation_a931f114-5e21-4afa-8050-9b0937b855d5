using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using CoffeeShopWebsite.Data;
using CoffeeShopWebsite.Models;

namespace CoffeeShopWebsite.Controllers
{
    public class ProductsController : Controller
    {
        private readonly CoffeeShopContext _context;

        public ProductsController(CoffeeShopContext context)
        {
            _context = context;
        }

        // GET: Products
        public async Task<IActionResult> Index(int? categoryId, string searchString, string sortOrder)
        {
            ViewBag.CurrentSort = sortOrder;
            ViewBag.NameSortParm = String.IsNullOrEmpty(sortOrder) ? "name_desc" : "";
            ViewBag.PriceSortParm = sortOrder == "Price" ? "price_desc" : "Price";
            ViewBag.CurrentFilter = searchString;
            ViewBag.CurrentCategory = categoryId;

            var products = from p in _context.Products.Include(p => p.Category)
                          where p.IsActive
                          select p;

            // Filter by category
            if (categoryId.HasValue)
            {
                products = products.Where(p => p.CategoryId == categoryId);
            }

            // Filter by search string
            if (!String.IsNullOrEmpty(searchString))
            {
                products = products.Where(p => p.Name.Contains(searchString) || 
                                             p.Description.Contains(searchString));
            }

            // Sort products
            switch (sortOrder)
            {
                case "name_desc":
                    products = products.OrderByDescending(p => p.Name);
                    break;
                case "Price":
                    products = products.OrderBy(p => p.Price);
                    break;
                case "price_desc":
                    products = products.OrderByDescending(p => p.Price);
                    break;
                default:
                    products = products.OrderBy(p => p.Name);
                    break;
            }

            // Get categories for filter dropdown
            ViewBag.Categories = await _context.Categories
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();

            return View(await products.ToListAsync());
        }

        // GET: Products/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var product = await _context.Products
                .Include(p => p.Category)
                .FirstOrDefaultAsync(m => m.Id == id && m.IsActive);

            if (product == null)
            {
                return NotFound();
            }

            // Get related products from same category
            ViewBag.RelatedProducts = await _context.Products
                .Where(p => p.CategoryId == product.CategoryId && p.Id != product.Id && p.IsActive)
                .Take(4)
                .ToListAsync();

            return View(product);
        }

        // GET: Products by Category
        public async Task<IActionResult> Category(int id)
        {
            var category = await _context.Categories
                .FirstOrDefaultAsync(c => c.Id == id && c.IsActive);

            if (category == null)
            {
                return NotFound();
            }

            var products = await _context.Products
                .Include(p => p.Category)
                .Where(p => p.CategoryId == id && p.IsActive)
                .OrderBy(p => p.Name)
                .ToListAsync();

            ViewBag.CategoryName = category.Name;
            ViewBag.CategoryDescription = category.Description;

            return View("Index", products);
        }

        // GET: Featured Products
        public async Task<IActionResult> Featured()
        {
            var featuredProducts = await _context.Products
                .Include(p => p.Category)
                .Where(p => p.IsFeatured && p.IsActive)
                .OrderBy(p => p.Name)
                .ToListAsync();

            ViewBag.PageTitle = "Sản phẩm nổi bật";
            return View("Index", featuredProducts);
        }

        // POST: Add to Cart
        [HttpPost]
        public async Task<IActionResult> AddToCart(int productId, int quantity = 1, string? notes = null)
        {
            var product = await _context.Products.FindAsync(productId);
            if (product == null || !product.IsActive)
            {
                return Json(new { success = false, message = "Sản phẩm không tồn tại" });
            }

            if (product.StockQuantity < quantity)
            {
                return Json(new { success = false, message = "Không đủ hàng trong kho" });
            }

            // Get session ID for guest users
            string sessionId = HttpContext.Session.Id;

            // Check if item already exists in cart
            var existingCartItem = await _context.CartItems
                .FirstOrDefaultAsync(c => c.ProductId == productId && c.SessionId == sessionId);

            if (existingCartItem != null)
            {
                existingCartItem.Quantity += quantity;
                existingCartItem.Notes = notes;
            }
            else
            {
                var cartItem = new CartItem
                {
                    ProductId = productId,
                    Quantity = quantity,
                    Notes = notes,
                    SessionId = sessionId,
                    AddedAt = DateTime.Now
                };
                _context.CartItems.Add(cartItem);
            }

            await _context.SaveChangesAsync();

            // Get cart count for response
            var cartCount = await _context.CartItems
                .Where(c => c.SessionId == sessionId)
                .SumAsync(c => c.Quantity);

            return Json(new { 
                success = true, 
                message = "Đã thêm vào giỏ hàng", 
                cartCount = cartCount 
            });
        }
    }
}
