using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using CoffeeShopWebsite.Data;
using CoffeeShopWebsite.Models;
using CoffeeShopWebsite.ViewModels;

namespace CoffeeShopWebsite.Controllers
{
    public class ProductsController : Controller
    {
        private readonly CoffeeShopContext _context;

        public ProductsController(CoffeeShopContext context)
        {
            _context = context;
        }

        // GET: Products
        public async Task<IActionResult> Index(int? categoryId, string searchString, string sortOrder,
            decimal? minPrice, decimal? maxPrice, int page = 1, int pageSize = 12)
        {
            var viewModel = new ProductListViewModel
            {
                CategoryId = categoryId,
                SearchString = searchString,
                SortOrder = sortOrder,
                MinPrice = minPrice,
                MaxPrice = maxPrice,
                CurrentPage = page,
                PageSize = pageSize
            };

            var products = from p in _context.Products.Include(p => p.Category)
                          where p.IsActive
                          select p;

            // Filter by category
            if (categoryId.HasValue)
            {
                products = products.Where(p => p.CategoryId == categoryId);
                var category = await _context.Categories.FindAsync(categoryId);
                if (category != null)
                {
                    viewModel.CategoryName = category.Name;
                    viewModel.CategoryDescription = category.Description;
                }
            }

            // Filter by search string
            if (!String.IsNullOrEmpty(searchString))
            {
                products = products.Where(p => p.Name.Contains(searchString) ||
                                             p.Description.Contains(searchString));
            }

            // Filter by price range
            if (minPrice.HasValue)
            {
                products = products.Where(p => p.Price >= minPrice.Value);
            }
            if (maxPrice.HasValue)
            {
                products = products.Where(p => p.Price <= maxPrice.Value);
            }

            // Sort products
            switch (sortOrder)
            {
                case "name_desc":
                    products = products.OrderByDescending(p => p.Name);
                    break;
                case "Price":
                    products = products.OrderBy(p => p.Price);
                    break;
                case "price_desc":
                    products = products.OrderByDescending(p => p.Price);
                    break;
                default:
                    products = products.OrderBy(p => p.Name);
                    break;
            }

            // Get total count for pagination
            viewModel.TotalItems = await products.CountAsync();
            viewModel.TotalPages = (int)Math.Ceiling((double)viewModel.TotalItems / pageSize);

            // Apply pagination
            var pagedProducts = await products
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            viewModel.Products = pagedProducts;

            // Get categories for filter dropdown
            viewModel.Categories = await _context.Categories
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();

            // Get price range for filters
            var allProducts = await _context.Products.Where(p => p.IsActive).ToListAsync();
            if (allProducts.Any())
            {
                viewModel.MinPriceRange = allProducts.Min(p => p.Price);
                viewModel.MaxPriceRange = allProducts.Max(p => p.Price);
            }

            return View(viewModel);
        }

        // GET: Products/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var product = await _context.Products
                .Include(p => p.Category)
                .FirstOrDefaultAsync(m => m.Id == id && m.IsActive);

            if (product == null)
            {
                return NotFound();
            }

            // Get related products from same category
            ViewBag.RelatedProducts = await _context.Products
                .Where(p => p.CategoryId == product.CategoryId && p.Id != product.Id && p.IsActive)
                .Take(4)
                .ToListAsync();

            return View(product);
        }

        // GET: Products by Category
        public async Task<IActionResult> Category(int id, string searchString, string sortBy, int page = 1, int pageSize = 12)
        {
            var category = await _context.Categories
                .FirstOrDefaultAsync(c => c.Id == id && c.IsActive);

            if (category == null)
            {
                return NotFound();
            }

            var viewModel = new ProductListViewModel
            {
                CategoryId = id,
                Category = category,
                SearchString = searchString,
                SortBy = sortBy,
                CurrentPage = page,
                PageSize = pageSize
            };

            var products = from p in _context.Products.Include(p => p.Category)
                          where p.IsActive && p.CategoryId == id
                          select p;

            // Filter by search string
            if (!String.IsNullOrEmpty(searchString))
            {
                products = products.Where(p => p.Name.Contains(searchString) ||
                                             p.Description.Contains(searchString));
            }

            // Sort products
            switch (sortBy)
            {
                case "name_desc":
                    products = products.OrderByDescending(p => p.Name);
                    break;
                case "price":
                    products = products.OrderBy(p => p.Price);
                    break;
                case "price_desc":
                    products = products.OrderByDescending(p => p.Price);
                    break;
                case "newest":
                    products = products.OrderByDescending(p => p.CreatedAt);
                    break;
                default:
                    products = products.OrderBy(p => p.Name);
                    break;
            }

            // Get total count
            viewModel.TotalCount = await products.CountAsync();
            viewModel.TotalPages = (int)Math.Ceiling((double)viewModel.TotalCount / pageSize);

            // Get products for current page
            viewModel.Products = await products
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return View("CategoryView", viewModel);
        }

        // GET: Featured Products
        public async Task<IActionResult> Featured(string sortOrder, int page = 1, int pageSize = 12)
        {
            var viewModel = new ProductListViewModel
            {
                SortOrder = sortOrder,
                CurrentPage = page,
                PageSize = pageSize,
                PageTitle = "Sản phẩm nổi bật"
            };

            var products = from p in _context.Products.Include(p => p.Category)
                          where p.IsFeatured && p.IsActive
                          select p;

            // Sort products
            switch (sortOrder)
            {
                case "name_desc":
                    products = products.OrderByDescending(p => p.Name);
                    break;
                case "Price":
                    products = products.OrderBy(p => p.Price);
                    break;
                case "price_desc":
                    products = products.OrderByDescending(p => p.Price);
                    break;
                default:
                    products = products.OrderBy(p => p.Name);
                    break;
            }

            // Get total count for pagination
            viewModel.TotalItems = await products.CountAsync();
            viewModel.TotalPages = (int)Math.Ceiling((double)viewModel.TotalItems / pageSize);

            // Apply pagination
            var pagedProducts = await products
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            viewModel.Products = pagedProducts;

            // Get categories for filter dropdown
            viewModel.Categories = await _context.Categories
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();

            return View("Index", viewModel);
        }

        // POST: Add to Cart
        [HttpPost]
        public async Task<IActionResult> AddToCart(int productId, int quantity = 1, string? notes = null)
        {
            var product = await _context.Products.FindAsync(productId);
            if (product == null || !product.IsActive)
            {
                return Json(new { success = false, message = "Sản phẩm không tồn tại" });
            }

            if (product.StockQuantity < quantity)
            {
                return Json(new { success = false, message = "Không đủ hàng trong kho" });
            }

            // Get session ID for guest users
            string sessionId = HttpContext.Session.Id;

            // Check if item already exists in cart
            var existingCartItem = await _context.CartItems
                .FirstOrDefaultAsync(c => c.ProductId == productId && c.SessionId == sessionId);

            if (existingCartItem != null)
            {
                existingCartItem.Quantity += quantity;
                existingCartItem.Notes = notes;
            }
            else
            {
                var cartItem = new CartItem
                {
                    ProductId = productId,
                    Quantity = quantity,
                    Notes = notes,
                    SessionId = sessionId,
                    AddedAt = DateTime.Now
                };
                _context.CartItems.Add(cartItem);
            }

            await _context.SaveChangesAsync();

            // Get cart count for response
            var cartCount = await _context.CartItems
                .Where(c => c.SessionId == sessionId)
                .SumAsync(c => c.Quantity);

            return Json(new { 
                success = true, 
                message = "Đã thêm vào giỏ hàng", 
                cartCount = cartCount 
            });
        }
    }
}
