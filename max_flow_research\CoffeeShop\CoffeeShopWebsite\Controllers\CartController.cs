using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using CoffeeShopWebsite.Data;
using CoffeeShopWebsite.Models;
using Microsoft.AspNetCore.Identity;
using CoffeeShopWebsite.ViewModels;

namespace CoffeeShopWebsite.Controllers
{
    public class CartController : Controller
    {
        private readonly CoffeeShopContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public CartController(CoffeeShopContext context, UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        // GET: Cart
        public async Task<IActionResult> Index()
        {
            var cartItemsQuery = await GetUserCartItems();
            var cartItems = await cartItemsQuery
                .OrderBy(c => c.AddedAt)
                .ToListAsync();

            return View(cartItems);
        }

        // POST: Update Cart Item Quantity
        [HttpPost]
        public async Task<IActionResult> UpdateQuantity(int cartItemId, int quantity)
        {
            if (quantity <= 0)
            {
                return Json(new { success = false, message = "Số lượng phải lớn hơn 0" });
            }

            string sessionId = HttpContext.Session.Id;
            var cartItem = await _context.CartItems
                .Include(c => c.Product)
                .FirstOrDefaultAsync(c => c.Id == cartItemId && c.SessionId == sessionId);

            if (cartItem == null)
            {
                return Json(new { success = false, message = "Không tìm thấy sản phẩm trong giỏ hàng" });
            }

            if (cartItem.Product.StockQuantity < quantity)
            {
                return Json(new { success = false, message = "Không đủ hàng trong kho" });
            }

            cartItem.Quantity = quantity;
            await _context.SaveChangesAsync();

            // Calculate new totals
            var itemTotal = cartItem.Quantity * cartItem.Product.Price;
            var cartTotal = await GetCartTotal(sessionId);
            var cartCount = await GetCartCount(sessionId);

            return Json(new { 
                success = true, 
                itemTotal = itemTotal.ToString("N0"),
                cartTotal = cartTotal.ToString("N0"),
                cartCount = cartCount
            });
        }

        // POST: Remove Cart Item
        [HttpPost]
        public async Task<IActionResult> RemoveItem(int cartItemId)
        {
            string sessionId = HttpContext.Session.Id;
            var cartItem = await _context.CartItems
                .FirstOrDefaultAsync(c => c.Id == cartItemId && c.SessionId == sessionId);

            if (cartItem == null)
            {
                return Json(new { success = false, message = "Không tìm thấy sản phẩm trong giỏ hàng" });
            }

            _context.CartItems.Remove(cartItem);
            await _context.SaveChangesAsync();

            var cartTotal = await GetCartTotal(sessionId);
            var cartCount = await GetCartCount(sessionId);

            return Json(new { 
                success = true, 
                message = "Đã xóa sản phẩm khỏi giỏ hàng",
                cartTotal = cartTotal.ToString("N0"),
                cartCount = cartCount
            });
        }

        // POST: Clear Cart
        [HttpPost]
        public async Task<IActionResult> ClearCart()
        {
            string sessionId = HttpContext.Session.Id;
            var cartItems = await _context.CartItems
                .Where(c => c.SessionId == sessionId)
                .ToListAsync();

            _context.CartItems.RemoveRange(cartItems);
            await _context.SaveChangesAsync();

            return Json(new { success = true, message = "Đã xóa tất cả sản phẩm khỏi giỏ hàng" });
        }

        // GET: Checkout
        public async Task<IActionResult> Checkout()
        {
            string sessionId = HttpContext.Session.Id;
            
            var cartItems = await _context.CartItems
                .Include(c => c.Product)
                .Where(c => c.SessionId == sessionId)
                .ToListAsync();

            if (!cartItems.Any())
            {
                TempData["ErrorMessage"] = "Giỏ hàng của bạn đang trống";
                return RedirectToAction("Index");
            }

            // Check stock availability
            foreach (var item in cartItems)
            {
                if (item.Product.StockQuantity < item.Quantity)
                {
                    TempData["ErrorMessage"] = $"Sản phẩm {item.Product.Name} không đủ hàng trong kho";
                    return RedirectToAction("Index");
                }
            }

            ViewBag.CartItems = cartItems;
            ViewBag.CartTotal = cartItems.Sum(c => c.TotalPrice);

            return View(new Order());
        }

        // POST: Process Checkout
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Checkout(Order order)
        {
            string sessionId = HttpContext.Session.Id;
            
            var cartItems = await _context.CartItems
                .Include(c => c.Product)
                .Where(c => c.SessionId == sessionId)
                .ToListAsync();

            if (!cartItems.Any())
            {
                TempData["ErrorMessage"] = "Giỏ hàng của bạn đang trống";
                return RedirectToAction("Index");
            }

            if (ModelState.IsValid)
            {
                // Generate order number
                order.OrderNumber = GenerateOrderNumber();
                order.OrderDate = DateTime.Now;
                order.TotalAmount = cartItems.Sum(c => c.TotalPrice);
                order.Status = OrderStatus.Pending;

                _context.Orders.Add(order);
                await _context.SaveChangesAsync();

                // Create order items
                foreach (var cartItem in cartItems)
                {
                    var orderItem = new OrderItem
                    {
                        OrderId = order.Id,
                        ProductId = cartItem.ProductId,
                        Quantity = cartItem.Quantity,
                        UnitPrice = cartItem.Product.Price,
                        Notes = cartItem.Notes
                    };
                    _context.OrderItems.Add(orderItem);

                    // Update product stock
                    cartItem.Product.StockQuantity -= cartItem.Quantity;
                }

                // Clear cart
                _context.CartItems.RemoveRange(cartItems);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = $"Đặt hàng thành công! Mã đơn hàng: {order.OrderNumber}";
                return RedirectToAction("OrderConfirmation", new { orderNumber = order.OrderNumber });
            }

            ViewBag.CartItems = cartItems;
            ViewBag.CartTotal = cartItems.Sum(c => c.TotalPrice);
            return View(order);
        }

        // GET: Order Confirmation
        public async Task<IActionResult> OrderConfirmation(string orderNumber)
        {
            var order = await _context.Orders
                .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
                .FirstOrDefaultAsync(o => o.OrderNumber == orderNumber);

            if (order == null)
            {
                return NotFound();
            }

            return View(order);
        }

        // Helper methods
        private async Task<IQueryable<CartItem>> GetUserCartItems()
        {
            var user = await _userManager.GetUserAsync(User);
            string sessionId = HttpContext.Session.Id;

            if (user != null)
            {
                // For authenticated users, get items by UserId or SessionId
                return _context.CartItems
                    .Include(c => c.Product)
                    .ThenInclude(p => p.Category)
                    .Where(c => c.UserId == user.Id || c.SessionId == sessionId);
            }
            else
            {
                // For guest users, get items by SessionId only
                return _context.CartItems
                    .Include(c => c.Product)
                    .ThenInclude(p => p.Category)
                    .Where(c => c.SessionId == sessionId);
            }
        }

        private async Task<decimal> GetCartTotal(string sessionId)
        {
            var user = await _userManager.GetUserAsync(User);

            if (user != null)
            {
                return await _context.CartItems
                    .Include(c => c.Product)
                    .Where(c => c.UserId == user.Id || c.SessionId == sessionId)
                    .SumAsync(c => c.Quantity * c.Product.Price);
            }
            else
            {
                return await _context.CartItems
                    .Include(c => c.Product)
                    .Where(c => c.SessionId == sessionId)
                    .SumAsync(c => c.Quantity * c.Product.Price);
            }
        }

        private async Task<int> GetCartCount(string sessionId)
        {
            var user = await _userManager.GetUserAsync(User);

            if (user != null)
            {
                return await _context.CartItems
                    .Where(c => c.UserId == user.Id || c.SessionId == sessionId)
                    .SumAsync(c => c.Quantity);
            }
            else
            {
                return await _context.CartItems
                    .Where(c => c.SessionId == sessionId)
                    .SumAsync(c => c.Quantity);
            }
        }

        private string GenerateOrderNumber()
        {
            return "ORD" + DateTime.Now.ToString("yyyyMMddHHmmss");
        }
    }
}
