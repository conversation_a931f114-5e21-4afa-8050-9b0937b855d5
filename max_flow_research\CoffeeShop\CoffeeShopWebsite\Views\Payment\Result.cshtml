@model CoffeeShopWebsite.ViewModels.PaymentStatusViewModel
@{
    ViewData["Title"] = "Kết quả thanh toán";
    var isSuccess = Model.Status == CoffeeShopWebsite.ViewModels.PaymentStatus.Completed;
    var isPending = Model.Status == CoffeeShopWebsite.ViewModels.PaymentStatus.Pending;
    var isFailed = Model.Status == CoffeeShopWebsite.ViewModels.PaymentStatus.Failed || Model.Status == CoffeeShopWebsite.ViewModels.PaymentStatus.Cancelled;
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-4">
                @if (isSuccess)
                {
                    <div class="mb-3">
                        <i class="fas fa-check-circle fa-4x text-success"></i>
                    </div>
                    <h2 class="text-success">Thanh toán thành công!</h2>
                    <p class="lead">Cảm ơn bạn đã thanh toán. Đơn hàng của bạn đã được x<PERSON>c nhận.</p>
                }
                else if (isPending)
                {
                    <div class="mb-3">
                        <i class="fas fa-clock fa-4x text-warning"></i>
                    </div>
                    <h2 class="text-warning">Đang chờ thanh toán</h2>
                    <p class="lead">@Model.Message</p>
                }
                else if (isFailed)
                {
                    <div class="mb-3">
                        <i class="fas fa-times-circle fa-4x text-danger"></i>
                    </div>
                    <h2 class="text-danger">Thanh toán thất bại</h2>
                    <p class="lead">@Model.Message</p>
                }
                else
                {
                    <div class="mb-3">
                        <i class="fas fa-spinner fa-spin fa-4x text-info"></i>
                    </div>
                    <h2 class="text-info">Đang xử lý thanh toán</h2>
                    <p class="lead">Vui lòng chờ trong giây lát...</p>
                }
            </div>

            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-receipt me-2"></i>Thông tin thanh toán</h4>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Mã đơn hàng:</strong> @Model.OrderNumber
                        </div>
                        <div class="col-md-6">
                            <strong>Số tiền:</strong> @Model.Amount.ToString("N0") VNĐ
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Phương thức:</strong> 
                            @switch (Model.PaymentMethod)
                            {
                                case PaymentMethod.Cash:
                                    <span><i class="fas fa-money-bill-wave me-1"></i>Thanh toán khi nhận hàng</span>
                                    break;
                                case PaymentMethod.BankTransfer:
                                    <span><i class="fas fa-university me-1"></i>Chuyển khoản ngân hàng</span>
                                    break;
                                case PaymentMethod.CreditCard:
                                    <span><i class="fas fa-credit-card me-1"></i>Thẻ tín dụng</span>
                                    break;
                                case PaymentMethod.EWallet:
                                    <span><i class="fas fa-mobile-alt me-1"></i>Ví điện tử</span>
                                    break;
                            }
                        </div>
                        <div class="col-md-6">
                            <strong>Trạng thái:</strong> 
                            @if (isSuccess)
                            {
                                <span class="badge bg-success">Thành công</span>
                            }
                            else if (isPending)
                            {
                                <span class="badge bg-warning">Chờ thanh toán</span>
                            }
                            else if (isFailed)
                            {
                                <span class="badge bg-danger">Thất bại</span>
                            }
                            else
                            {
                                <span class="badge bg-info">Đang xử lý</span>
                            }
                        </div>
                    </div>
                    @if (!string.IsNullOrEmpty(Model.TransactionId))
                    {
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>Mã giao dịch:</strong> @Model.TransactionId
                            </div>
                            <div class="col-md-6">
                                <strong>Thời gian:</strong> @Model.ProcessedAt.ToString("dd/MM/yyyy HH:mm")
                            </div>
                        </div>
                    }
                </div>
            </div>

            <!-- Bank Transfer Instructions -->
            @if (Model.PaymentMethod == PaymentMethod.BankTransfer && Model.BankInfo != null)
            {
                <div class="card mt-3">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-university me-2"></i>Thông tin chuyển khoản</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Vui lòng chuyển khoản theo thông tin bên dưới để hoàn tất thanh toán
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Ngân hàng:</strong> @Model.BankInfo.BankName</p>
                                <p><strong>Số tài khoản:</strong> 
                                    <span class="badge bg-dark fs-6">@Model.BankInfo.AccountNumber</span>
                                    <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyToClipboard('@Model.BankInfo.AccountNumber')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Tên tài khoản:</strong> @Model.BankInfo.AccountName</p>
                                <p><strong>Nội dung chuyển khoản:</strong> 
                                    <span class="badge bg-dark fs-6">@Model.BankInfo.TransferContent</span>
                                    <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyToClipboard('@Model.BankInfo.TransferContent')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            }

            <!-- E-Wallet QR Code -->
            @if (Model.PaymentMethod == PaymentMethod.EWallet && Model.EWalletInfo != null)
            {
                <div class="card mt-3">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-mobile-alt me-2"></i>Thanh toán qua @Model.EWalletInfo.WalletType</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Quét mã QR bên dưới bằng ứng dụng @Model.EWalletInfo.WalletType để thanh toán
                        </div>
                        @if (!string.IsNullOrEmpty(Model.EWalletInfo.QRCodeUrl))
                        {
                            <img src="@Model.EWalletInfo.QRCodeUrl" alt="QR Code" class="img-fluid mb-3" style="max-width: 200px;">
                        }
                        @if (!string.IsNullOrEmpty(Model.EWalletInfo.DeepLink))
                        {
                            <div>
                                <a href="@Model.EWalletInfo.DeepLink" class="btn btn-primary">
                                    <i class="fas fa-external-link-alt me-1"></i>Mở ứng dụng @Model.EWalletInfo.WalletType
                                </a>
                            </div>
                        }
                    </div>
                </div>
            }

            <!-- Actions -->
            <div class="text-center mt-4">
                @if (isPending && !string.IsNullOrEmpty(Model.TransactionId))
                {
                    <div class="mb-3">
                        <button class="btn btn-outline-primary" onclick="checkPaymentStatus()">
                            <i class="fas fa-sync-alt me-1"></i>Kiểm tra trạng thái thanh toán
                        </button>
                    </div>
                }

                <div class="d-flex justify-content-center gap-2">
                    <a asp-controller="Products" asp-action="Index" class="btn btn-primary">
                        <i class="fas fa-shopping-bag me-1"></i>Tiếp tục mua sắm
                    </a>
                    @if (User.Identity.IsAuthenticated)
                    {
                        <a asp-controller="Order" asp-action="Index" class="btn btn-outline-primary">
                            <i class="fas fa-history me-1"></i>Xem đơn hàng
                        </a>
                    }
                    else
                    {
                        <a asp-controller="Order" asp-action="Track" class="btn btn-outline-primary">
                            <i class="fas fa-search me-1"></i>Tra cứu đơn hàng
                        </a>
                    }
                    @if (isFailed)
                    {
                        <a asp-controller="Payment" asp-action="Process" asp-route-orderId="@ViewBag.OrderId" class="btn btn-warning">
                            <i class="fas fa-redo me-1"></i>Thử lại
                        </a>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showToast('success', 'Đã sao chép vào clipboard');
            }, function(err) {
                console.error('Could not copy text: ', err);
                showToast('error', 'Không thể sao chép');
            });
        }

        function checkPaymentStatus() {
            var transactionId = '@Model.TransactionId';
            if (!transactionId) return;

            $.ajax({
                url: '@Url.Action("CheckStatus", "Payment")',
                type: 'GET',
                data: { transactionId: transactionId },
                success: function(response) {
                    if (response.isCompleted) {
                        showToast('success', 'Thanh toán đã hoàn tất!');
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    } else if (response.isFailed) {
                        showToast('error', 'Thanh toán thất bại');
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    } else {
                        showToast('info', 'Vẫn đang chờ thanh toán');
                    }
                },
                error: function() {
                    showToast('error', 'Không thể kiểm tra trạng thái thanh toán');
                }
            });
        }

        function showToast(type, message) {
            var alertClass = type === 'success' ? 'alert-success' : 
                           type === 'error' ? 'alert-danger' : 'alert-info';
            var toast = $('<div class="alert ' + alertClass + ' alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">' +
                         '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                         message + '</div>');

            $('body').append(toast);

            setTimeout(function() {
                toast.alert('close');
            }, 3000);
        }

        // Auto refresh for pending payments
        @if (isPending && !string.IsNullOrEmpty(Model.TransactionId))
        {
            <text>
            setInterval(function() {
                checkPaymentStatus();
            }, 30000); // Check every 30 seconds
            </text>
        }
    </script>
}
