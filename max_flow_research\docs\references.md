# TÀI LIỆU THAM KHẢO

## Tà<PERSON> liệu chính

### 1. <PERSON><PERSON><PERSON> gi<PERSON> khoa

**[1] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, C. <PERSON>, <PERSON>, R. L., & <PERSON>, C. (2009)**  
*Introduction to Algorithms* (3rd Edition)  
MIT Press  
**Chương 26:** Maximum Flow  
- Trang 708-740: <PERSON><PERSON><PERSON><PERSON><PERSON>-Fulkerson
- Trang 727-732: <PERSON><PERSON><PERSON> lý <PERSON>-Flow Min-Cut
- Trang 733-738: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>-Karp

**[2] <PERSON>, J., & <PERSON>, E. (2005)**  
*Algorithm Design*  
Addison-Wesley  
**Chương 7:** Network Flow  
- Trang 337-406: <PERSON><PERSON><PERSON> to<PERSON> luồng cực đại và ứng dụng
- Trang 347-355: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>-Fulkerson chi tiết
- Trang 378-385: <PERSON><PERSON><PERSON><PERSON> dụng thực tế

**[3] <PERSON><PERSON><PERSON>, R. <PERSON>, <PERSON>, T. <PERSON>, & <PERSON>, J<PERSON> (1993)**  
*Network Flows: Theory, Algorithms, and Applications*  
Prentice Hall  
- Chương 6: Maximum Flow Algorithms
- Chương 7: Maximum Flow Applications

### 2. <PERSON><PERSON><PERSON> b<PERSON><PERSON> kho<PERSON> họ<PERSON> gốc

**[4] Ford, <PERSON>. <PERSON>., & <PERSON>lkerson, <PERSON>. R. (1956)**  
*Maximal flow through a network*  
Canadian Journal of Mathematics, 8, 399-404  
**DOI:** 10.4153/<PERSON>JM-1956-045-5  
- <PERSON>ài báo đầu tiên gi<PERSON>i thiệu thu<PERSON>t toán Ford-<PERSON>lkerson
- Chứng minh đ<PERSON>nh lý <PERSON>-Flow <PERSON>-<PERSON>

**[5] <PERSON>s, J., & Karp, R. M. (1972)**  
*Theoretical improvements in algorithmic efficiency for network flow problems*  
Journal of the ACM, 19(2), 248-264  
**DOI:** 10.1145/321694.321699  
- Cải tiến thuật toán Ford-Fulkerson với BFS
- Đạt độ phức tạp O(VE²)

**[6] Dinic, E. A. (1970)**  
*Algorithm for solution of a problem of maximum flow in networks with power estimation*  
Soviet Mathematics Doklady, 11, 1277-1280  
- Thuật toán Dinic với độ phức tạp O(V²E)
- Sử dụng khái niệm layered network

### 3. Tài liệu bổ sung

**[7] Sedgewick, R., & Wayne, K. (2011)**  
*Algorithms* (4th Edition)  
Addison-Wesley  
**Chương 6.4:** Maximum Flow  
- Cài đặt thực tế bằng Java
- Phân tích hiệu suất chi tiết

**[8] Skiena, S. S. (2008)**  
*The Algorithm Design Manual* (2nd Edition)  
Springer  
**Chương 6.1:** Network Flow  
- Ứng dụng thực tế của bài toán luồng
- So sánh các thuật toán khác nhau

## Tài liệu trực tuyến

### 1. Khóa học và bài giảng

**[9] MIT OpenCourseWare**  
*6.046J Design and Analysis of Algorithms*  
Lecture 13: Network Flow  
URL: https://ocw.mit.edu/courses/electrical-engineering-and-computer-science/6-046j-design-and-analysis-of-algorithms-spring-2015/

**[10] Stanford CS161**  
*Design and Analysis of Algorithms*  
Lecture Notes on Network Flow  
URL: http://web.stanford.edu/class/cs161/

**[11] Princeton Algorithms Course**  
*Algorithms, Part II by Robert Sedgewick*  
Week 3: Maximum Flow and Minimum Cut  
URL: https://www.coursera.org/learn/algorithms-part2

### 2. Tài liệu kỹ thuật

**[12] GeeksforGeeks**  
*Ford-Fulkerson Algorithm for Maximum Flow Problem*  
URL: https://www.geeksforgeeks.org/ford-fulkerson-algorithm-for-maximum-flow-problem/

**[13] CP-Algorithms**  
*Maximum flow - Ford-Fulkerson and Edmonds-Karp*  
URL: https://cp-algorithms.com/graph/edmonds_karp.html

**[14] Topcoder Tutorial**  
*Maximum Flow*  
URL: https://www.topcoder.com/community/competitive-programming/tutorials/maximum-flow-section-1/

## Ứng dụng và mở rộng

### 1. Bipartite Matching

**[15] Hopcroft, J. E., & Karp, R. M. (1973)**  
*An n^{5/2} algorithm for maximum matchings in bipartite graphs*  
SIAM Journal on Computing, 2(4), 225-231

### 2. Min-Cost Max-Flow

**[16] Orlin, J. B. (1993)**  
*A faster strongly polynomial minimum cost flow algorithm*  
Operations Research, 41(2), 338-350

### 3. Push-Relabel Algorithm

**[17] Goldberg, A. V., & Tarjan, R. E. (1988)**  
*A new approach to the maximum-flow problem*  
Journal of the ACM, 35(4), 921-940

## Công cụ và thư viện

### 1. Thư viện C++

**[18] Boost Graph Library**  
*Maximum Flow Algorithms*  
URL: https://www.boost.org/doc/libs/1_75_0/libs/graph/doc/maximum_flow.html

**[19] LEMON Graph Library**  
*Network Flow Algorithms*  
URL: https://lemon.cs.elte.hu/pub/doc/1.3.1/a00608.html

### 2. Visualization Tools

**[20] Graphviz**  
*Graph Visualization Software*  
URL: https://graphviz.org/

**[21] NetworkX (Python)**  
*Network Analysis in Python*  
URL: https://networkx.org/documentation/stable/reference/algorithms/flow.html

## Bài tập và thực hành

### 1. Online Judges

**[22] LeetCode**  
- Problem 1579: Remove Max Number of Edges to Keep Graph Fully Traversable
- Problem 1489: Find Critical and Pseudo-Critical Edges in MST

**[23] Codeforces**  
- Problem 546E: Soldier and Traveling
- Problem 1082G: Petya and Graph

**[24] SPOJ**  
- FASTFLOW: Fast Maximum Flow
- MATCHING: Maximum Bipartite Matching

### 2. Competitive Programming

**[25] Halim, S., & Halim, F. (2013)**  
*Competitive Programming 3*  
Lulu Press  
**Chương 4.7:** Network Flow

## Tài liệu tiếng Việt

### 1. Sách giáo khoa

**[26] Lê Minh Hoàng (2008)**  
*Giải thuật và lập trình*  
NXB Đại học Quốc gia Hà Nội  
**Chương 6:** Đồ thị và các thuật toán trên đồ thị

**[27] Nguyễn Đức Nghĩa, Nguyễn Tô Thành (2004)**  
*Cấu trúc dữ liệu và giải thuật*  
NXB Đại học Quốc gia TP.HCM

### 2. Tài liệu trực tuyến

**[28] VNOI Wiki**  
*Luồng cực đại (Maximum Flow)*  
URL: https://vnoi.info/wiki/algo/graph-theory/maximum-flow

**[29] Codelearn.io**  
*Thuật toán Ford-Fulkerson*  
URL: https://codelearn.io/sharing/thuat-toan-ford-fulkerson

## Ghi chú về tính chính xác

- Tất cả tài liệu tham khảo đã được kiểm tra tính chính xác tại thời điểm viết báo cáo
- Các URL có thể thay đổi theo thời gian
- Khuyến nghị truy cập thông qua thư viện trường hoặc cơ sở dữ liệu học thuật
- Một số tài liệu có thể yêu cầu quyền truy cập hoặc đăng ký

## Đóng góp và phản hồi

Nếu phát hiện lỗi hoặc có đề xuất bổ sung tài liệu tham khảo, vui lòng liên hệ:
- Email: [<EMAIL>]
- GitHub: [github_username]

---

*Cập nhật lần cuối: [Ngày/Tháng/Năm]*
