#include "graph.h"

/**
 * Constructor khởi tạo đồ thị với V đỉnh
 */
Graph::Graph(int vertices) : V(vertices) {
    capacity.assign(V, vector<int>(V, 0));
    flow.assign(V, vector<int>(V, 0));
}

/**
 * Thêm cạnh vào đồ thị
 */
void Graph::addEdge(int from, int to, int cap) {
    if (from >= 0 && from < V && to >= 0 && to < V && cap >= 0) {
        capacity[from][to] = cap;
    }
}

/**
 * Thuật toán BFS để tìm đường tăng luồng trong đồ thị dư
 */
bool Graph::bfs(int source, int sink, vector<int>& parent) {
    vector<bool> visited(V, false);
    queue<int> q;
    
    q.push(source);
    visited[source] = true;
    parent[source] = -1;
    
    while (!q.empty()) {
        int u = q.front();
        q.pop();
        
        for (int v = 0; v < V; v++) {
            // <PERSON>ểm tra cạnh trong đồ thị dư: capacity - flow > 0
            if (!visited[v] && capacity[u][v] - flow[u][v] > 0) {
                if (v == sink) {
                    parent[v] = u;
                    return true;
                }
                q.push(v);
                visited[v] = true;
                parent[v] = u;
            }
        }
    }
    
    return false;
}

/**
 * Thuật toán Ford-Fulkerson chính
 */
int Graph::fordFulkerson(int source, int sink) {
    vector<int> parent(V);
    int maxFlow = 0;
    
    cout << "\n=== BẮT ĐẦU THUẬT TOÁN FORD-FULKERSON ===\n";
    cout << "Nguồn: " << source << ", Đích: " << sink << "\n\n";
    
    int iteration = 1;
    
    // Tìm đường tăng luồng cho đến khi không còn đường nào
    while (bfs(source, sink, parent)) {
        cout << "--- Lần lặp " << iteration << " ---\n";
        
        // Tìm dung lượng tối thiểu trên đường tăng luồng
        int pathFlow = INT_MAX;
        cout << "Đường tăng luồng: ";
        
        // In đường đi từ sink về source
        vector<int> path;
        for (int v = sink; v != source; v = parent[v]) {
            path.push_back(v);
            int u = parent[v];
            pathFlow = min(pathFlow, capacity[u][v] - flow[u][v]);
        }
        path.push_back(source);
        
        // In đường đi từ source đến sink
        for (int i = path.size() - 1; i >= 0; i--) {
            cout << path[i];
            if (i > 0) cout << " -> ";
        }
        cout << "\nLuồng có thể tăng: " << pathFlow << "\n";
        
        // Cập nhật luồng dọc theo đường tăng luồng
        for (int v = sink; v != source; v = parent[v]) {
            int u = parent[v];
            flow[u][v] += pathFlow;  // Tăng luồng thuận
            flow[v][u] -= pathFlow;  // Giảm luồng ngược
        }
        
        maxFlow += pathFlow;
        cout << "Tổng luồng hiện tại: " << maxFlow << "\n\n";
        iteration++;
    }
    
    cout << "=== KẾT THÚC THUẬT TOÁN ===\n";
    cout << "LUỒNG CỰC ĐẠI: " << maxFlow << "\n\n";
    
    return maxFlow;
}

/**
 * In ma trận dung lượng
 */
void Graph::printCapacity() {
    cout << "\n=== MA TRẬN DUNG LƯỢNG ===\n";
    cout << "    ";
    for (int i = 0; i < V; i++) {
        cout << setw(4) << i;
    }
    cout << "\n";
    
    for (int i = 0; i < V; i++) {
        cout << setw(2) << i << ": ";
        for (int j = 0; j < V; j++) {
            cout << setw(4) << capacity[i][j];
        }
        cout << "\n";
    }
    cout << "\n";
}

/**
 * In ma trận luồng kết quả
 */
void Graph::printFlow() {
    cout << "\n=== MA TRẬN LUỒNG KẾT QUẢ ===\n";
    cout << "    ";
    for (int i = 0; i < V; i++) {
        cout << setw(4) << i;
    }
    cout << "\n";
    
    for (int i = 0; i < V; i++) {
        cout << setw(2) << i << ": ";
        for (int j = 0; j < V; j++) {
            cout << setw(4) << flow[i][j];
        }
        cout << "\n";
    }
    cout << "\n";
}

/**
 * In thông tin chi tiết về luồng trên từng cạnh
 */
void Graph::printFlowDetails() {
    cout << "\n=== CHI TIẾT LUỒNG TRÊN CÁC CẠNH ===\n";
    cout << "Cạnh\t\tLuồng/Dung lượng\tTỷ lệ sử dụng\n";
    cout << "--------------------------------------------\n";
    
    for (int i = 0; i < V; i++) {
        for (int j = 0; j < V; j++) {
            if (capacity[i][j] > 0) {
                double utilization = (double)flow[i][j] / capacity[i][j] * 100;
                cout << "(" << i << "," << j << ")\t\t" 
                     << flow[i][j] << "/" << capacity[i][j] 
                     << "\t\t" << fixed << setprecision(1) 
                     << utilization << "%\n";
            }
        }
    }
    cout << "\n";
}

/**
 * Tìm và in lát cắt tối thiểu
 */
void Graph::findMinCut(int source) {
    vector<int> parent(V);
    vector<bool> visited(V, false);
    queue<int> q;
    
    // BFS từ source trong đồ thị dư cuối cùng
    q.push(source);
    visited[source] = true;
    
    while (!q.empty()) {
        int u = q.front();
        q.pop();
        
        for (int v = 0; v < V; v++) {
            if (!visited[v] && capacity[u][v] - flow[u][v] > 0) {
                q.push(v);
                visited[v] = true;
            }
        }
    }
    
    cout << "\n=== LÁT CẮT TỐI THIỂU ===\n";
    cout << "Các cạnh trong lát cắt tối thiểu:\n";
    
    int minCutCapacity = 0;
    for (int i = 0; i < V; i++) {
        for (int j = 0; j < V; j++) {
            if (visited[i] && !visited[j] && capacity[i][j] > 0) {
                cout << "(" << i << "," << j << ") với dung lượng " << capacity[i][j] << "\n";
                minCutCapacity += capacity[i][j];
            }
        }
    }
    
    cout << "Tổng dung lượng lát cắt tối thiểu: " << minCutCapacity << "\n";
    
    cout << "\nTập đỉnh phía nguồn: {";
    bool first = true;
    for (int i = 0; i < V; i++) {
        if (visited[i]) {
            if (!first) cout << ", ";
            cout << i;
            first = false;
        }
    }
    cout << "}\n";
    
    cout << "Tập đỉnh phía đích: {";
    first = true;
    for (int i = 0; i < V; i++) {
        if (!visited[i]) {
            if (!first) cout << ", ";
            cout << i;
            first = false;
        }
    }
    cout << "}\n\n";
}

/**
 * Reset luồng về 0 cho tất cả các cạnh
 */
void Graph::resetFlow() {
    for (int i = 0; i < V; i++) {
        for (int j = 0; j < V; j++) {
            flow[i][j] = 0;
        }
    }
}
