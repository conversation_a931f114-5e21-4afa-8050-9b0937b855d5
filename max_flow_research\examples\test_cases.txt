# FILE TEST CASES CHO THUẬT TOÁN FORD-FULKERSON
# Tác giả: [Tên sinh viên]
# Mô tả: Các test case để kiểm tra tính đúng đắn của thuật toán

================================================================================
TEST CASE 1: MẠNG ĐƠN GIẢN (4 ĐỈNH)
================================================================================
Mô tả: Mạng luồng cơ bản với 4 đỉnh, minh họa các khái niệm cơ bản

Số đỉnh: 4
Đỉnh nguồn: 0 (s)
Đỉnh đích: 3 (t)

<PERSON><PERSON><PERSON> c<PERSON>nh (từ -> đến: dung lượng):
0 -> 1: 16
0 -> 2: 13
1 -> 2: 10
1 -> 3: 12
2 -> 1: 4
2 -> 3: 14

Sơ đồ mạng:
    s(0) --16--> 1 --12--> t(3)
     |           |          ^
     13         10         14
     |           v          |
     +-----> 2 --4--> 1 ----+
             |              
             +------14------>

Kết quả mong đợi:
- Luồng cực đại: 23
- Lát cắt tối thiểu: {0,2} và {1,3} với tổng dung lượng 23

================================================================================
TEST CASE 2: MẠNG PHỨC TẠP (6 ĐỈNH)
================================================================================
Mô tả: Mạng phức tạp hơn với nhiều đường đi và nút trung gian

Số đỉnh: 6
Đỉnh nguồn: 0 (s)
Đỉnh đích: 5 (t)

Các cạnh (từ -> đến: dung lượng):
0 -> 1: 10
0 -> 2: 10
1 -> 2: 2
1 -> 3: 4
1 -> 4: 8
2 -> 4: 9
3 -> 5: 10
4 -> 3: 6
4 -> 5: 10

Sơ đồ mạng:
         1 --4--> 3
       / |        | \
      10 2        6  10
     /   |        |   \
    s    v        v    t
     \   4 --6--> 4   /
      10 |        |  10
       \ v        | /
         2 --9----+

Kết quả mong đợi:
- Luồng cực đại: 19
- Nhiều đường tăng luồng khác nhau

================================================================================
TEST CASE 3: MẠNG VỚI ĐƯỜNG ĐI SONG SONG (5 ĐỈNH)
================================================================================
Mô tả: Mạng với nhiều đường đi song song từ nguồn đến đích

Số đỉnh: 5
Đỉnh nguồn: 0 (s)
Đỉnh đích: 4 (t)

Các cạnh (từ -> đến: dung lượng):
0 -> 1: 20
0 -> 2: 30
1 -> 2: 40
1 -> 3: 30
2 -> 3: 20
2 -> 4: 30
3 -> 4: 20

Sơ đồ mạng:
    s --20--> 1 --30--> 3
    |         |         |
    30       40        20
    |         v         v
    +---> 2 --20--> 3 --+
          |             |
          +----30-----> t

Kết quả mong đợi:
- Luồng cực đại: 50
- Tận dụng được tất cả các đường đi

================================================================================
TEST CASE 4: MẠNG THÁCH THỨC (7 ĐỈNH)
================================================================================
Mô tả: Mạng phức tạp để test hiệu suất thuật toán

Số đỉnh: 7
Đỉnh nguồn: 0 (s)
Đỉnh đích: 6 (t)

Các cạnh (từ -> đến: dung lượng):
0 -> 1: 15
0 -> 2: 20
0 -> 3: 25
1 -> 2: 10
1 -> 4: 12
2 -> 3: 8
2 -> 4: 15
2 -> 5: 18
3 -> 5: 20
4 -> 5: 5
4 -> 6: 25
5 -> 6: 30

Kết quả mong đợi:
- Luồng cực đại: 48
- Nhiều lần lặp để tìm được kết quả tối ưu

================================================================================
TEST CASE 5: MẠNG ĐẶC BIỆT - BOTTLENECK
================================================================================
Mô tả: Mạng có nút thắt cổ chai (bottleneck)

Số đỉnh: 5
Đỉnh nguồn: 0 (s)
Đỉnh đích: 4 (t)

Các cạnh (từ -> đến: dung lượng):
0 -> 1: 100
0 -> 2: 100
1 -> 3: 1    # Nút thắt cổ chai
2 -> 3: 1    # Nút thắt cổ chai
3 -> 4: 100

Sơ đồ mạng:
    s --100--> 1 --1--> 3 --100--> t
    |                   ^
    100                 1
    |                   |
    +-----> 2 ----------+

Kết quả mong đợi:
- Luồng cực đại: 2 (bị giới hạn bởi nút thắt)
- Minh họa tầm quan trọng của lát cắt tối thiểu

================================================================================
HƯỚNG DẪN NHẬP TEST CASE TÙY CHỈNH
================================================================================

Khi chọn "Nhập mạng luồng mới" trong chương trình:

1. Nhập số đỉnh (≥ 2)
2. Nhập số cạnh
3. Với mỗi cạnh, nhập: đỉnh_xuất_phát đỉnh_đích dung_lượng
4. Nhập đỉnh nguồn (thường là 0)
5. Nhập đỉnh đích (thường là n-1)

Ví dụ nhập cho Test Case 1:
Số đỉnh: 4
Số cạnh: 6
Cạnh 1: 0 1 16
Cạnh 2: 0 2 13
Cạnh 3: 1 2 10
Cạnh 4: 1 3 12
Cạnh 5: 2 1 4
Cạnh 6: 2 3 14
Đỉnh nguồn: 0
Đỉnh đích: 3

================================================================================
GHI CHÚ VỀ KẾT QUẢ
================================================================================

Chương trình sẽ hiển thị:
1. Ma trận dung lượng ban đầu
2. Quá trình tìm đường tăng luồng từng bước
3. Ma trận luồng kết quả
4. Chi tiết luồng trên từng cạnh với tỷ lệ sử dụng
5. Lát cắt tối thiểu và phân chia đỉnh

Lưu ý:
- Luồng cực đại = Dung lượng lát cắt tối thiểu (Định lý Max-Flow Min-Cut)
- Thuật toán đảm bảo tìm được kết quả tối ưu
- Thời gian chạy phụ thuộc vào cấu trúc mạng và giá trị luồng cực đại
