@model CoffeeShopWebsite.Models.Order
@{
    ViewData["Title"] = "Xác nhận đơn hàng";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-4">
                <div class="mb-3">
                    <i class="fas fa-check-circle fa-4x text-success"></i>
                </div>
                <h2 class="text-success">Đặt hàng thành công!</h2>
                <p class="lead">Cảm ơn bạn đã đặt hàng. Chúng tôi sẽ xử lý đơn hàng của bạn trong thời gian sớm nhất.</p>
            </div>

            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-receipt me-2"></i>Thông tin đơn hàng</h4>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Mã đơn hàng:</strong> @Model.OrderNumber
                        </div>
                        <div class="col-md-6">
                            <strong>Ngày đặt:</strong> @Model.OrderDate.ToString("dd/MM/yyyy HH:mm")
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Trạng thái:</strong> 
                            <span class="badge bg-warning">@Model.Status.ToString()</span>
                        </div>
                        <div class="col-md-6">
                            <strong>Phương thức thanh toán:</strong> @Model.PaymentMethod.ToString()
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-user me-2"></i>Thông tin khách hàng</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Họ tên:</strong> @Model.CustomerName</p>
                            <p><strong>Email:</strong> @Model.CustomerEmail</p>
                            <p><strong>Điện thoại:</strong> @Model.CustomerPhone</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Địa chỉ giao hàng:</strong></p>
                            <p>@Model.DeliveryAddress</p>
                        </div>
                    </div>
                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                        <p><strong>Ghi chú:</strong> @Model.Notes</p>
                    }
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-list me-2"></i>Chi tiết đơn hàng</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Sản phẩm</th>
                                    <th>Đơn giá</th>
                                    <th>Số lượng</th>
                                    <th>Thành tiền</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.OrderItems)
                                {
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if (!string.IsNullOrEmpty(item.Product.ImageUrl))
                                                {
                                                    <img src="@item.Product.ImageUrl" class="me-2 rounded" style="width: 50px; height: 50px; object-fit: cover;" alt="@item.Product.Name">
                                                }
                                                <div>
                                                    <strong>@item.Product.Name</strong>
                                                    <br>
                                                    <small class="text-muted">@item.Product.Category.Name</small>
                                                    @if (!string.IsNullOrEmpty(item.Notes))
                                                    {
                                                        <br>
                                                        <small class="text-info">Ghi chú: @item.Notes</small>
                                                    }
                                                </div>
                                            </div>
                                        </td>
                                        <td>@item.UnitPrice.ToString("N0") VNĐ</td>
                                        <td>@item.Quantity</td>
                                        <td><strong>@item.TotalPrice.ToString("N0") VNĐ</strong></td>
                                    </tr>
                                }
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3">Tổng cộng:</th>
                                    <th class="text-primary">@Model.TotalAmount.ToString("N0") VNĐ</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Lưu ý:</strong> Vui lòng lưu lại mã đơn hàng <strong>@Model.OrderNumber</strong> để tra cứu đơn hàng.
                    @if (User.Identity.IsAuthenticated)
                    {
                        <span>Bạn có thể xem lịch sử đơn hàng trong tài khoản của mình.</span>
                    }
                    else
                    {
                        <span>Bạn có thể tra cứu đơn hàng bằng mã đơn hàng và email.</span>
                    }
                </div>

                <div class="d-flex justify-content-center gap-2">
                    <a asp-controller="Products" asp-action="Index" class="btn btn-primary">
                        <i class="fas fa-shopping-bag me-1"></i>Tiếp tục mua sắm
                    </a>
                    @if (User.Identity.IsAuthenticated)
                    {
                        <a asp-controller="Order" asp-action="Index" class="btn btn-outline-primary">
                            <i class="fas fa-history me-1"></i>Xem đơn hàng
                        </a>
                    }
                    else
                    {
                        <a asp-controller="Order" asp-action="Track" class="btn btn-outline-primary">
                            <i class="fas fa-search me-1"></i>Tra cứu đơn hàng
                        </a>
                    }
                    <button onclick="window.print()" class="btn btn-outline-secondary">
                        <i class="fas fa-print me-1"></i>In đơn hàng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    @@media print {
        .btn, .alert {
            display: none !important;
        }
    }
</style>
