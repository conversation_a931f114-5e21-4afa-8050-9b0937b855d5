using CoffeeShopWebsite.Models;
using CoffeeShopWebsite.ViewModels;

namespace CoffeeShopWebsite.Services
{
    public interface IPaymentService
    {
        Task<PaymentResult> ProcessPaymentAsync(Order order, PaymentRequest request);
        Task<PaymentResult> ProcessCashPaymentAsync(Order order);
        Task<PaymentResult> ProcessBankTransferAsync(Order order, BankTransferRequest request);
        Task<PaymentResult> ProcessCreditCardAsync(Order order, CreditCardRequest request);
        Task<PaymentResult> ProcessEWalletAsync(Order order, EWalletRequest request);
        Task<bool> VerifyPaymentAsync(string transactionId, PaymentMethod method);
        Task<PaymentStatus> GetPaymentStatusAsync(string transactionId);
    }

    public class PaymentService : IPaymentService
    {
        private readonly ILogger<PaymentService> _logger;

        public PaymentService(ILogger<PaymentService> logger)
        {
            _logger = logger;
        }

        public async Task<PaymentResult> ProcessPaymentAsync(Order order, PaymentRequest request)
        {
            try
            {
                return order.PaymentMethod switch
                {
                    PaymentMethod.Cash => await ProcessCashPaymentAsync(order),
                    PaymentMethod.BankTransfer => await ProcessBankTransferAsync(order, request.BankTransfer),
                    PaymentMethod.CreditCard => await ProcessCreditCardAsync(order, request.CreditCard),
                    PaymentMethod.EWallet => await ProcessEWalletAsync(order, request.EWallet),
                    _ => new PaymentResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "Phương thức thanh toán không được hỗ trợ"
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing payment for order {OrderId}", order.Id);
                return new PaymentResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Có lỗi xảy ra trong quá trình thanh toán"
                };
            }
        }

        public async Task<PaymentResult> ProcessCashPaymentAsync(Order order)
        {
            // COD - Cash on Delivery
            // No actual payment processing needed, just mark as pending payment
            await Task.Delay(100); // Simulate processing time

            return new PaymentResult
            {
                IsSuccess = true,
                TransactionId = $"COD_{order.OrderNumber}_{DateTime.Now:yyyyMMddHHmmss}",
                PaymentMethod = PaymentMethod.Cash,
                Amount = order.TotalAmount,
                Status = PaymentStatus.Pending,
                Message = "Đơn hàng sẽ được thanh toán khi nhận hàng"
            };
        }

        public async Task<PaymentResult> ProcessBankTransferAsync(Order order, BankTransferRequest request)
        {
            // Simulate bank transfer processing
            await Task.Delay(500);

            // In real implementation, you would:
            // 1. Validate bank account information
            // 2. Create transfer request to bank API
            // 3. Handle response and update payment status

            if (request == null)
            {
                return new PaymentResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Thông tin chuyển khoản không hợp lệ"
                };
            }

            // Simulate successful bank transfer
            return new PaymentResult
            {
                IsSuccess = true,
                TransactionId = $"BANK_{DateTime.Now:yyyyMMddHHmmss}",
                PaymentMethod = PaymentMethod.BankTransfer,
                Amount = order.TotalAmount,
                Status = PaymentStatus.Pending,
                Message = "Vui lòng chuyển khoản theo thông tin đã cung cấp",
                BankInfo = new BankInfo
                {
                    BankName = "Ngân hàng TMCP Á Châu (ACB)",
                    AccountNumber = "*********",
                    AccountName = "COFFEE SHOP",
                    TransferContent = $"Thanh toan don hang {order.OrderNumber}"
                }
            };
        }

        public async Task<PaymentResult> ProcessCreditCardAsync(Order order, CreditCardRequest request)
        {
            // Simulate credit card processing
            await Task.Delay(1000);

            if (request == null || string.IsNullOrEmpty(request.CardNumber))
            {
                return new PaymentResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Thông tin thẻ tín dụng không hợp lệ"
                };
            }

            // Basic card validation (in real app, use proper validation)
            if (request.CardNumber.Length < 16)
            {
                return new PaymentResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Số thẻ không hợp lệ"
                };
            }

            // Simulate payment gateway processing
            var random = new Random();
            var isSuccess = random.Next(1, 101) <= 95; // 95% success rate

            if (isSuccess)
            {
                return new PaymentResult
                {
                    IsSuccess = true,
                    TransactionId = $"CC_{DateTime.Now:yyyyMMddHHmmss}",
                    PaymentMethod = PaymentMethod.CreditCard,
                    Amount = order.TotalAmount,
                    Status = PaymentStatus.Completed,
                    Message = "Thanh toán thành công"
                };
            }
            else
            {
                return new PaymentResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Thanh toán bị từ chối. Vui lòng kiểm tra thông tin thẻ hoặc liên hệ ngân hàng."
                };
            }
        }

        public async Task<PaymentResult> ProcessEWalletAsync(Order order, EWalletRequest request)
        {
            // Simulate e-wallet processing
            await Task.Delay(800);

            if (request == null || string.IsNullOrEmpty(request.WalletType))
            {
                return new PaymentResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Thông tin ví điện tử không hợp lệ"
                };
            }

            // Simulate different e-wallet providers
            var walletName = request.WalletType.ToLower() switch
            {
                "momo" => "MoMo",
                "zalopay" => "ZaloPay",
                "vnpay" => "VNPay",
                _ => "Ví điện tử"
            };

            // Generate QR code URL (in real app, integrate with actual e-wallet APIs)
            var qrCodeUrl = $"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=PAY_{order.OrderNumber}_{order.TotalAmount}";

            return new PaymentResult
            {
                IsSuccess = true,
                TransactionId = $"EW_{request.WalletType.ToUpper()}_{DateTime.Now:yyyyMMddHHmmss}",
                PaymentMethod = PaymentMethod.EWallet,
                Amount = order.TotalAmount,
                Status = PaymentStatus.Pending,
                Message = $"Vui lòng quét mã QR để thanh toán qua {walletName}",
                QRCodeUrl = qrCodeUrl,
                EWalletInfo = new EWalletInfo
                {
                    WalletType = walletName,
                    QRCodeUrl = qrCodeUrl,
                    DeepLink = $"{request.WalletType}://pay?amount={order.TotalAmount}&order={order.OrderNumber}"
                }
            };
        }

        public async Task<bool> VerifyPaymentAsync(string transactionId, PaymentMethod method)
        {
            // Simulate payment verification
            await Task.Delay(300);

            // In real implementation, verify with payment provider
            return !string.IsNullOrEmpty(transactionId);
        }

        public async Task<PaymentStatus> GetPaymentStatusAsync(string transactionId)
        {
            // Simulate checking payment status
            await Task.Delay(200);

            if (string.IsNullOrEmpty(transactionId))
                return PaymentStatus.Failed;

            // Simulate different statuses based on transaction type
            if (transactionId.StartsWith("COD_"))
                return PaymentStatus.Pending;
            if (transactionId.StartsWith("CC_"))
                return PaymentStatus.Completed;
            if (transactionId.StartsWith("BANK_"))
                return PaymentStatus.Pending;
            if (transactionId.StartsWith("EW_"))
                return PaymentStatus.Pending;

            return PaymentStatus.Failed;
        }
    }
}
