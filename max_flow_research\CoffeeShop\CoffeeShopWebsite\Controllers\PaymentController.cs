using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using CoffeeShopWebsite.Data;
using CoffeeShopWebsite.Models;
using CoffeeShopWebsite.ViewModels;
using CoffeeShopWebsite.Services;
using Microsoft.AspNetCore.Identity;

namespace CoffeeShopWebsite.Controllers
{
    public class PaymentController : Controller
    {
        private readonly CoffeeShopContext _context;
        private readonly IPaymentService _paymentService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<PaymentController> _logger;

        public PaymentController(
            CoffeeShopContext context,
            IPaymentService paymentService,
            UserManager<ApplicationUser> userManager,
            ILogger<PaymentController> logger)
        {
            _context = context;
            _paymentService = paymentService;
            _userManager = userManager;
            _logger = logger;
        }

        // GET: Payment/Process/{orderId}
        public async Task<IActionResult> Process(int orderId)
        {
            var order = await _context.Orders
                .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
                .FirstOrDefaultAsync(o => o.Id == orderId);

            if (order == null)
            {
                return NotFound();
            }

            // Check if order belongs to current user (if logged in)
            var user = await _userManager.GetUserAsync(User);
            if (user != null && order.UserId != user.Id)
            {
                return Forbid();
            }

            var viewModel = new PaymentViewModel
            {
                Order = order,
                PaymentRequest = new PaymentRequest()
            };

            return View(viewModel);
        }

        // POST: Payment/Process
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Process(PaymentViewModel model)
        {
            var order = await _context.Orders
                .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
                .FirstOrDefaultAsync(o => o.Id == model.Order.Id);

            if (order == null)
            {
                return NotFound();
            }

            // Check if order belongs to current user (if logged in)
            var user = await _userManager.GetUserAsync(User);
            if (user != null && order.UserId != user.Id)
            {
                return Forbid();
            }

            // Process payment
            var paymentResult = await _paymentService.ProcessPaymentAsync(order, model.PaymentRequest);

            if (paymentResult.IsSuccess)
            {
                // Update order with payment info
                order.IsPaid = paymentResult.Status == PaymentStatus.Completed;
                
                // You might want to store payment transaction details in a separate table
                // For now, we'll store basic info in order notes
                if (!string.IsNullOrEmpty(paymentResult.TransactionId))
                {
                    order.Notes = (order.Notes ?? "") + $"\nTransaction ID: {paymentResult.TransactionId}";
                }

                await _context.SaveChangesAsync();

                // Redirect to payment result page
                return RedirectToAction("Result", new { 
                    orderNumber = order.OrderNumber, 
                    transactionId = paymentResult.TransactionId 
                });
            }
            else
            {
                // Payment failed, show error
                model.PaymentResult = paymentResult;
                ModelState.AddModelError("", paymentResult.ErrorMessage ?? "Thanh toán thất bại");
                return View(model);
            }
        }

        // GET: Payment/Result
        public async Task<IActionResult> Result(string orderNumber, string? transactionId)
        {
            var order = await _context.Orders
                .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
                .FirstOrDefaultAsync(o => o.OrderNumber == orderNumber);

            if (order == null)
            {
                return NotFound();
            }

            // Check if order belongs to current user (if logged in)
            var user = await _userManager.GetUserAsync(User);
            if (user != null && order.UserId != user.Id)
            {
                return Forbid();
            }

            PaymentStatus status = PaymentStatus.Pending;
            string message = "Đang xử lý thanh toán";

            if (!string.IsNullOrEmpty(transactionId))
            {
                status = await _paymentService.GetPaymentStatusAsync(transactionId);
                message = status switch
                {
                    PaymentStatus.Completed => "Thanh toán thành công",
                    PaymentStatus.Pending => "Đang chờ thanh toán",
                    PaymentStatus.Processing => "Đang xử lý thanh toán",
                    PaymentStatus.Failed => "Thanh toán thất bại",
                    PaymentStatus.Cancelled => "Thanh toán đã bị hủy",
                    _ => "Trạng thái không xác định"
                };
            }

            var viewModel = new PaymentStatusViewModel
            {
                OrderNumber = orderNumber,
                TransactionId = transactionId,
                Status = status,
                PaymentMethod = order.PaymentMethod,
                Amount = order.TotalAmount,
                ProcessedAt = DateTime.Now,
                Message = message
            };

            return View(viewModel);
        }

        // GET: Payment/Status/{transactionId}
        [HttpGet]
        public async Task<IActionResult> CheckStatus(string transactionId)
        {
            if (string.IsNullOrEmpty(transactionId))
            {
                return BadRequest("Transaction ID is required");
            }

            var status = await _paymentService.GetPaymentStatusAsync(transactionId);
            
            return Json(new { 
                status = status.ToString(),
                isCompleted = status == PaymentStatus.Completed,
                isFailed = status == PaymentStatus.Failed || status == PaymentStatus.Cancelled
            });
        }

        // POST: Payment/Verify
        [HttpPost]
        public async Task<IActionResult> Verify(string transactionId, PaymentMethod method)
        {
            if (string.IsNullOrEmpty(transactionId))
            {
                return BadRequest("Transaction ID is required");
            }

            var isValid = await _paymentService.VerifyPaymentAsync(transactionId, method);
            
            if (isValid)
            {
                // Update order payment status
                // This would typically be done via webhook from payment provider
                var order = await _context.Orders
                    .FirstOrDefaultAsync(o => o.Notes != null && o.Notes.Contains(transactionId));

                if (order != null)
                {
                    order.IsPaid = true;
                    order.Status = OrderStatus.Confirmed;
                    await _context.SaveChangesAsync();
                }
            }

            return Json(new { success = isValid });
        }

        // GET: Payment/Methods
        public IActionResult Methods()
        {
            var methods = new List<object>
            {
                new { 
                    Value = PaymentMethod.Cash, 
                    Name = "Thanh toán khi nhận hàng (COD)",
                    Description = "Thanh toán bằng tiền mặt khi nhận hàng",
                    Icon = "fas fa-money-bill-wave"
                },
                new { 
                    Value = PaymentMethod.BankTransfer, 
                    Name = "Chuyển khoản ngân hàng",
                    Description = "Chuyển khoản qua ngân hàng hoặc internet banking",
                    Icon = "fas fa-university"
                },
                new { 
                    Value = PaymentMethod.CreditCard, 
                    Name = "Thẻ tín dụng/ghi nợ",
                    Description = "Thanh toán bằng thẻ Visa, MasterCard",
                    Icon = "fas fa-credit-card"
                },
                new { 
                    Value = PaymentMethod.EWallet, 
                    Name = "Ví điện tử",
                    Description = "MoMo, ZaloPay, VNPay",
                    Icon = "fas fa-mobile-alt"
                }
            };

            return Json(methods);
        }
    }
}
