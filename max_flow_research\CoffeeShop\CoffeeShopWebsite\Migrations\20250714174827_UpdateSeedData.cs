﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace CoffeeShopWebsite.Migrations
{
    /// <inheritdoc />
    public partial class UpdateSeedData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Categories",
                keyColumn: "Id",
                keyValue: 5);

            migrationBuilder.DeleteData(
                table: "Products",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "Products",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "Products",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "Products",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "Products",
                keyColumn: "Id",
                keyValue: 5);

            migrationBuilder.DeleteData(
                table: "Products",
                keyColumn: "Id",
                keyValue: 6);

            migrationBuilder.DeleteData(
                table: "Products",
                keyColumn: "Id",
                keyValue: 7);

            migrationBuilder.DeleteData(
                table: "Products",
                keyColumn: "Id",
                keyValue: 8);

            migrationBuilder.DeleteData(
                table: "Products",
                keyColumn: "Id",
                keyValue: 9);

            migrationBuilder.DeleteData(
                table: "Products",
                keyColumn: "Id",
                keyValue: 10);

            migrationBuilder.DeleteData(
                table: "Categories",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "Categories",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "Categories",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "Categories",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.InsertData(
                table: "AspNetRoles",
                columns: new[] { "Id", "ConcurrencyStamp", "Name", "NormalizedName" },
                values: new object[,]
                {
                    { "1", null, "Admin", "ADMIN" },
                    { "2", null, "Customer", "CUSTOMER" }
                });

            migrationBuilder.InsertData(
                table: "Coupons",
                columns: new[] { "Id", "Code", "CreatedAt", "CreatedBy", "Description", "EndDate", "IsForNewCustomersOnly", "MaxDiscountAmount", "MinOrderAmount", "Name", "RequiredCustomerLevel", "StartDate", "Status", "Type", "UsageLimit", "UsedCount", "Value" },
                values: new object[,]
                {
                    { 1, "WELCOME10", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, "Giảm 10% cho đơn hàng đầu tiên", new DateTime(2025, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified), true, 20000m, 50000m, "Chào mừng thành viên mới", null, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), 0, 0, null, 0, 10m },
                    { 2, "FREESHIP", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, "Miễn phí vận chuyển cho đơn hàng từ 100k", new DateTime(2025, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified), false, null, 100000m, "Miễn phí vận chuyển", null, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), 0, 2, null, 0, 0m },
                    { 3, "GOLD20", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, "Giảm 20% cho thành viên Gold", new DateTime(2025, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified), false, 50000m, 200000m, "Ưu đãi thành viên Gold", "Gold", new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), 0, 0, null, 0, 20m }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "1");

            migrationBuilder.DeleteData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "2");

            migrationBuilder.DeleteData(
                table: "Coupons",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "Coupons",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "Coupons",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.InsertData(
                table: "Categories",
                columns: new[] { "Id", "CreatedAt", "Description", "ImageUrl", "IsActive", "Name" },
                values: new object[,]
                {
                    { 1, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Các loại cà phê truyền thống và hiện đại", "https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=300&h=200&fit=crop", true, "Cà phê" },
                    { 2, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Các loại trà và trà sữa thơm ngon", "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop", true, "Trà" },
                    { 3, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Nước ép trái cây tươi 100% tự nhiên", "https://images.unsplash.com/photo-1613478223719-2ab802602423?w=300&h=200&fit=crop", true, "Nước ép" },
                    { 4, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Sinh tố các loại bổ dưỡng", "https://images.unsplash.com/photo-1505252585461-04db1eb84625?w=300&h=200&fit=crop", true, "Sinh tố" },
                    { 5, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Các loại đồ uống đá xay mát lạnh", "https://images.unsplash.com/photo-1551024506-0bccd828d307?w=300&h=200&fit=crop", true, "Đá xay" }
                });

            migrationBuilder.InsertData(
                table: "Products",
                columns: new[] { "Id", "CategoryId", "CreatedAt", "Description", "ImageUrl", "IsActive", "IsFeatured", "Name", "Price", "StockQuantity", "UpdatedAt" },
                values: new object[,]
                {
                    { 1, 1, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Cà phê đen truyền thống, đậm đà hương vị Việt Nam", "https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300&h=300&fit=crop", true, true, "Cà phê đen", 15000m, 100, null },
                    { 2, 1, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Cà phê sữa đá thơm ngon, ngọt ngào", "https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=300&h=300&fit=crop", true, true, "Cà phê sữa", 18000m, 100, null },
                    { 3, 1, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Cappuccino Ý với lớp foam mịn màng", "https://images.unsplash.com/photo-1572442388796-11668a67e53d?w=300&h=300&fit=crop", true, false, "Cappuccino", 35000m, 50, null },
                    { 4, 1, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Latte thơm ngon với nghệ thuật latte art", "https://images.unsplash.com/photo-1561047029-3000c68339ca?w=300&h=300&fit=crop", true, false, "Latte", 40000m, 50, null },
                    { 5, 2, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Trà đào cam sả tươi mát, thơm ngon", "https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=300&h=300&fit=crop", true, true, "Trà đào", 25000m, 80, null },
                    { 6, 2, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Trà sữa trân châu đường đen đậm đà", "https://images.unsplash.com/photo-1525385133512-2f3bdd039054?w=300&h=300&fit=crop", true, false, "Trà sữa trân châu", 30000m, 60, null },
                    { 7, 3, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Nước ép cam tươi 100% không đường", "https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=300&h=300&fit=crop", true, false, "Nước ép cam", 20000m, 40, null },
                    { 8, 3, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Nước ép dưa hấu tươi mát, giải nhiệt", "https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=300&h=300&fit=crop", true, false, "Nước ép dưa hấu", 22000m, 30, null },
                    { 9, 4, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Sinh tố bơ béo ngậy, bổ dưỡng", "https://images.unsplash.com/photo-1553530666-ba11a7da3888?w=300&h=300&fit=crop", true, false, "Sinh tố bơ", 28000m, 35, null },
                    { 10, 4, new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "Sinh tố xoài ngọt mát, thơm ngon", "https://images.unsplash.com/photo-1546173159-315724a31696?w=300&h=300&fit=crop", true, false, "Sinh tố xoài", 26000m, 40, null }
                });
        }
    }
}
