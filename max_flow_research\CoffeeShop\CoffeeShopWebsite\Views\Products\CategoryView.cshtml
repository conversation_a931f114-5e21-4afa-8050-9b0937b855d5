@model CoffeeShopWebsite.ViewModels.ProductListViewModel

@{
    ViewData["Title"] = Model.Category?.Name ?? "Danh mục sản phẩm";
}

<div class="container mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
            <li class="breadcrumb-item"><a asp-controller="Products" asp-action="Index">Sản phẩm</a></li>
            <li class="breadcrumb-item active" aria-current="page">@(Model.Category?.Name ?? "Danh mục")</li>
        </ol>
    </nav>

    <!-- Category Header -->
    @if (Model.Category != null)
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center py-5">
                        <h1 class="display-4 mb-3">
                            <i class="fas fa-coffee me-3"></i>@Model.Category.Name
                        </h1>
                        @if (!string.IsNullOrEmpty(Model.Category.Description))
                        {
                            <p class="lead">@Model.Category.Description</p>
                        }
                        <p class="mb-0">
                            <i class="fas fa-box me-2"></i>@Model.TotalCount sản phẩm có sẵn
                        </p>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Products Grid -->
    @if (Model.Products.Any())
    {
        <div class="row">
            @foreach (var product in Model.Products)
            {
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="position-relative">
                            @if (!string.IsNullOrEmpty(product.ImageUrl))
                            {
                                <img src="@product.ImageUrl" class="card-img-top" alt="@product.Name" style="height: 250px; object-fit: cover;">
                            }
                            else
                            {
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                                    <i class="fas fa-coffee fa-3x text-muted"></i>
                                </div>
                            }
                            
                            @if (product.IsFeatured)
                            {
                                <div class="position-absolute top-0 start-0 m-2">
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-star me-1"></i>Nổi bật
                                    </span>
                                </div>
                            }
                        </div>
                        
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">@product.Name</h5>
                            <p class="card-text text-muted flex-grow-1">@product.Description</p>
                            
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="h5 text-primary mb-0">@product.Price.ToString("N0") VNĐ</span>
                                <small class="text-muted">
                                    <i class="fas fa-box me-1"></i>Còn @product.StockQuantity
                                </small>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a asp-controller="Products" asp-action="Details" asp-route-id="@product.Id" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>Chi tiết
                                </a>
                                @if (product.StockQuantity > 0)
                                {
                                    <button class="btn btn-primary btn-sm" onclick="addToCart(@product.Id)">
                                        <i class="fas fa-cart-plus me-1"></i>Thêm vào giỏ
                                    </button>
                                }
                                else
                                {
                                    <button class="btn btn-secondary btn-sm" disabled>
                                        <i class="fas fa-times me-1"></i>Hết hàng
                                    </button>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h3>Không tìm thấy sản phẩm</h3>
            <p class="text-muted">Không có sản phẩm nào trong danh mục này.</p>
            <a asp-controller="Products" asp-action="Index" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>Xem tất cả sản phẩm
            </a>
        </div>
    }
</div>

<script>
    function addToCart(productId) {
        $.post('@Url.Action("AddToCart", "Cart")', { productId: productId, quantity: 1 })
            .done(function(response) {
                if (response.success) {
                    alert('Đã thêm sản phẩm vào giỏ hàng!');
                } else {
                    alert(response.message || 'Có lỗi xảy ra!');
                }
            })
            .fail(function() {
                alert('Có lỗi xảy ra khi thêm sản phẩm!');
            });
    }
</script>
