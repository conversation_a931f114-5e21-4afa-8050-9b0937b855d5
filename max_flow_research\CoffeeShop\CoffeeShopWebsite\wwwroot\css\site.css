/* Root Variables */
:root {
    --primary-color: #8B4513;
    --secondary-color: #D2691E;
    --accent-color: #F4A460;
    --dark-color: #2C1810;
    --light-color: #F5F5DC;
    --warning-color: #FFC107;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

/* Base Styles */
html {
    font-size: 14px;
    position: relative;
    min-height: 100%;
}

@media (min-width: 768px) {
    html {
        font-size: 16px;
    }
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fafafa;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Focus Styles */
.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
    box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem var(--warning-color);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: var(--dark-color);
}

.display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
    font-weight: 700;
}

/* Buttons */
.btn {
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), var(--accent-color));
    color: var(--dark-color);
}

.btn-warning:hover {
    background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
    color: white;
}

.btn-outline-warning {
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.btn-outline-warning:hover {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

/* Cards */
.card {
    border: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

/* Navigation */
.navbar {
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: var(--warning-color);
    transition: all 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
    left: 0;
}

/* Dropdown */
.dropdown-menu {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

/* Forms */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--warning-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Utility Classes */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color), var(--accent-color));
}

/* Responsive */
@media (max-width: 768px) {
    .display-3 {
        font-size: 2.5rem;
    }

    .hero-section {
        text-align: center;
    }

    .hero-section .col-lg-6:first-child {
        margin-bottom: 2rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--warning-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Coffee Shop Custom Styles */
.hero-section {
  background: linear-gradient(135deg, #6f4e37, #8b4513);
  border-radius: 15px;
}

.category-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.product-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.navbar-brand {
  font-size: 1.5rem;
}

.btn-primary {
  background-color: #6f4e37;
  border-color: #6f4e37;
}

.btn-primary:hover {
  background-color: #5a3d2b;
  border-color: #5a3d2b;
}

.btn-outline-primary {
  color: #6f4e37;
  border-color: #6f4e37;
}

.btn-outline-primary:hover {
  background-color: #6f4e37;
  border-color: #6f4e37;
}

.text-primary {
  color: #6f4e37 !important;
}

.bg-primary {
  background-color: #6f4e37 !important;
}

.card-img-top {
  transition: transform 0.3s ease;
}

.card:hover .card-img-top {
  transform: scale(1.05);
}

.price-tag {
  font-weight: bold;
  color: #d2691e;
}

.cart-badge {
  font-size: 0.75rem;
}

/* Footer styles */
footer {
  margin-top: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-section h1 {
    font-size: 2rem;
  }

  .hero-section p {
    font-size: 1rem;
  }
}