html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Coffee Shop Custom Styles */
.hero-section {
  background: linear-gradient(135deg, #6f4e37, #8b4513);
  border-radius: 15px;
}

.category-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.product-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.navbar-brand {
  font-size: 1.5rem;
}

.btn-primary {
  background-color: #6f4e37;
  border-color: #6f4e37;
}

.btn-primary:hover {
  background-color: #5a3d2b;
  border-color: #5a3d2b;
}

.btn-outline-primary {
  color: #6f4e37;
  border-color: #6f4e37;
}

.btn-outline-primary:hover {
  background-color: #6f4e37;
  border-color: #6f4e37;
}

.text-primary {
  color: #6f4e37 !important;
}

.bg-primary {
  background-color: #6f4e37 !important;
}

.card-img-top {
  transition: transform 0.3s ease;
}

.card:hover .card-img-top {
  transform: scale(1.05);
}

.price-tag {
  font-weight: bold;
  color: #d2691e;
}

.cart-badge {
  font-size: 0.75rem;
}

/* Footer styles */
footer {
  margin-top: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-section h1 {
    font-size: 2rem;
  }

  .hero-section p {
    font-size: 1rem;
  }
}