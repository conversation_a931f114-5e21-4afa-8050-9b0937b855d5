@{
    ViewData["Title"] = "<PERSON>ên hệ";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h1 class="mb-0">
                        <i class="fas fa-phone me-2"></i>Liên hệ với chúng tôi
                    </h1>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Contact Information -->
                        <div class="col-md-6">
                            <h3>Thông tin liên hệ</h3>
                            
                            <div class="mb-3">
                                <h5><i class="fas fa-map-marker-alt text-danger me-2"></i>Địa chỉ</h5>
                                <p>123 Đ<PERSON>ờng Nguyễn Huệ, Quận 1, TP. <PERSON><PERSON><PERSON></p>
                            </div>

                            <div class="mb-3">
                                <h5><i class="fas fa-phone text-success me-2"></i><PERSON><PERSON><PERSON><PERSON> tho<PERSON></h5>
                                <p>
                                    <a href="tel:0123456789">0123 456 789</a><br>
                                    <a href="tel:0987654321">0987 654 321</a>
                                </p>
                            </div>

                            <div class="mb-3">
                                <h5><i class="fas fa-envelope text-warning me-2"></i>Email</h5>
                                <p>
                                    <a href="mailto:<EMAIL>"><EMAIL></a><br>
                                    <a href="mailto:<EMAIL>"><EMAIL></a>
                                </p>
                            </div>

                            <div class="mb-3">
                                <h5><i class="fas fa-clock text-info me-2"></i>Giờ mở cửa</h5>
                                <p>
                                    Thứ 2 - Thứ 6: 6:00 - 22:00<br>
                                    Thứ 7 - Chủ nhật: 6:00 - 23:00
                                </p>
                            </div>

                            <div class="mb-3">
                                <h5>Theo dõi chúng tôi</h5>
                                <div class="d-flex gap-2">
                                    <a href="#" class="btn btn-primary btn-sm">
                                        <i class="fab fa-facebook-f"></i> Facebook
                                    </a>
                                    <a href="#" class="btn btn-danger btn-sm">
                                        <i class="fab fa-instagram"></i> Instagram
                                    </a>
                                    <a href="#" class="btn btn-danger btn-sm">
                                        <i class="fab fa-youtube"></i> YouTube
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Form -->
                        <div class="col-md-6">
                            <h3>Gửi tin nhắn cho chúng tôi</h3>
                            
                            <form id="contactForm">
                                <div class="mb-3">
                                    <label for="fullName" class="form-label">Họ và tên *</label>
                                    <input type="text" class="form-control" id="fullName" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Số điện thoại</label>
                                    <input type="tel" class="form-control" id="phone">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="subject" class="form-label">Chủ đề *</label>
                                    <select class="form-select" id="subject" required>
                                        <option value="">Chọn chủ đề</option>
                                        <option value="general">Thông tin chung</option>
                                        <option value="order">Đặt hàng</option>
                                        <option value="complaint">Khiếu nại</option>
                                        <option value="suggestion">Góp ý</option>
                                        <option value="other">Khác</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="message" class="form-label">Nội dung tin nhắn *</label>
                                    <textarea class="form-control" id="message" rows="4" required></textarea>
                                </div>
                                
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="reset" class="btn btn-secondary">
                                        <i class="fas fa-undo me-1"></i>Làm lại
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-1"></i>Gửi tin nhắn
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- FAQ Section -->
                    <div class="row">
                        <div class="col-12">
                            <h3>Câu hỏi thường gặp</h3>
                            <div class="accordion" id="faqAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                            Làm thế nào để đặt hàng online?
                                        </button>
                                    </h2>
                                    <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            Bạn có thể đặt hàng online bằng cách truy cập trang sản phẩm, chọn sản phẩm yêu thích, 
                                            thêm vào giỏ hàng và tiến hành thanh toán.
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                            Thời gian giao hàng là bao lâu?
                                        </button>
                                    </h2>
                                    <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            Chúng tôi cam kết giao hàng trong vòng 30 phút đối với khu vực nội thành.
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                            Có chính sách đổi trả không?
                                        </button>
                                    </h2>
                                    <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            Chúng tôi có chính sách đổi trả trong vòng 24 giờ nếu sản phẩm có vấn đề về chất lượng.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.getElementById('contactForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const fullName = document.getElementById('fullName').value;
        const email = document.getElementById('email').value;
        const subject = document.getElementById('subject').value;
        const message = document.getElementById('message').value;
        
        if (!fullName || !email || !subject || !message) {
            alert('Vui lòng điền đầy đủ thông tin bắt buộc!');
            return;
        }
        
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Đang gửi...';
        submitBtn.disabled = true;
        
        setTimeout(() => {
            alert('Cảm ơn bạn đã liên hệ! Chúng tôi sẽ phản hồi trong thời gian sớm nhất.');
            this.reset();
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 2000);
    });
</script>
