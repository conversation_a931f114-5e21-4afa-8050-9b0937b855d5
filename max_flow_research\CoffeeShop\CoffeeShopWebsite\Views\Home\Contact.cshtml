@{
    ViewData["Title"] = "<PERSON>ên hệ";
}

<div class="container mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
            <li class="breadcrumb-item active" aria-current="page"><PERSON><PERSON>n hệ</li>
        </ol>
    </nav>

    <!-- Hero Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body text-center py-5">
                    <h1 class="display-4 mb-3">
                        <i class="fas fa-phone me-3"></i>Liên hệ với chúng tôi
                    </h1>
                    <p class="lead">Chúng tôi luôn sẵn sàng lắng nghe và hỗ trợ bạn</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Contact Information -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body p-4">
                    <h3 class="card-title text-primary mb-4">
                        <i class="fas fa-info-circle me-2"></i>Thông tin liên hệ
                    </h3>
                    
                    <div class="contact-item mb-4">
                        <div class="d-flex align-items-start">
                            <div class="contact-icon me-3">
                                <i class="fas fa-map-marker-alt fa-lg text-danger"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Địa chỉ</h6>
                                <p class="text-muted mb-0">
                                    123 Đường Nguyễn Huệ<br>
                                    Quận 1, TP. Hồ Chí Minh<br>
                                    Việt Nam
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="contact-item mb-4">
                        <div class="d-flex align-items-start">
                            <div class="contact-icon me-3">
                                <i class="fas fa-phone fa-lg text-success"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Điện thoại</h6>
                                <p class="text-muted mb-0">
                                    <a href="tel:0123456789" class="text-decoration-none">0123 456 789</a><br>
                                    <a href="tel:0987654321" class="text-decoration-none">0987 654 321</a>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="contact-item mb-4">
                        <div class="d-flex align-items-start">
                            <div class="contact-icon me-3">
                                <i class="fas fa-envelope fa-lg text-warning"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Email</h6>
                                <p class="text-muted mb-0">
                                    <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a><br>
                                    <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="contact-item mb-4">
                        <div class="d-flex align-items-start">
                            <div class="contact-icon me-3">
                                <i class="fas fa-clock fa-lg text-info"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Giờ mở cửa</h6>
                                <p class="text-muted mb-0">
                                    Thứ 2 - Thứ 6: 6:00 - 22:00<br>
                                    Thứ 7 - Chủ nhật: 6:00 - 23:00
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="social-links">
                        <h6 class="mb-3">Theo dõi chúng tôi</h6>
                        <div class="d-flex gap-3">
                            <a href="#" class="btn btn-outline-primary btn-sm rounded-circle" style="width: 40px; height: 40px;">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="btn btn-outline-danger btn-sm rounded-circle" style="width: 40px; height: 40px;">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="btn btn-outline-danger btn-sm rounded-circle" style="width: 40px; height: 40px;">
                                <i class="fab fa-youtube"></i>
                            </a>
                            <a href="#" class="btn btn-outline-dark btn-sm rounded-circle" style="width: 40px; height: 40px;">
                                <i class="fab fa-tiktok"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Form -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <h3 class="card-title text-primary mb-4">
                        <i class="fas fa-paper-plane me-2"></i>Gửi tin nhắn cho chúng tôi
                    </h3>
                    
                    <form id="contactForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="fullName" class="form-label">Họ và tên <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="fullName" name="fullName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Số điện thoại</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="subject" class="form-label">Chủ đề <span class="text-danger">*</span></label>
                                <select class="form-select" id="subject" name="subject" required>
                                    <option value="">Chọn chủ đề</option>
                                    <option value="general">Thông tin chung</option>
                                    <option value="order">Đặt hàng</option>
                                    <option value="complaint">Khiếu nại</option>
                                    <option value="suggestion">Góp ý</option>
                                    <option value="partnership">Hợp tác</option>
                                    <option value="other">Khác</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Nội dung tin nhắn <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="message" name="message" rows="6" required 
                                      placeholder="Vui lòng nhập nội dung tin nhắn của bạn..."></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="agreeTerms" name="agreeTerms" required>
                                <label class="form-check-label" for="agreeTerms">
                                    Tôi đồng ý với <a href="#" class="text-decoration-none">điều khoản sử dụng</a> và 
                                    <a href="#" class="text-decoration-none">chính sách bảo mật</a> <span class="text-danger">*</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="reset" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-2"></i>Làm lại
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Gửi tin nhắn
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    <h3 class="card-title text-primary p-4 mb-0">
                        <i class="fas fa-map-marked-alt me-2"></i>Vị trí của chúng tôi
                    </h3>
                    <div class="map-container" style="height: 400px; background: #f8f9fa;">
                        <div class="d-flex align-items-center justify-content-center h-100">
                            <div class="text-center">
                                <i class="fas fa-map-marker-alt fa-3x text-primary mb-3"></i>
                                <h5>Bản đồ sẽ được tích hợp tại đây</h5>
                                <p class="text-muted">123 Đường Nguyễn Huệ, Quận 1, TP. Hồ Chí Minh</p>
                                <a href="https://maps.google.com" target="_blank" class="btn btn-primary">
                                    <i class="fas fa-external-link-alt me-2"></i>Xem trên Google Maps
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="text-center mb-4">
                <i class="fas fa-question-circle me-2 text-primary"></i>Câu hỏi thường gặp
            </h3>
            <div class="accordion" id="faqAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                            Làm thế nào để đặt hàng online?
                        </button>
                    </h2>
                    <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Bạn có thể đặt hàng online bằng cách truy cập trang sản phẩm, chọn sản phẩm yêu thích, 
                            thêm vào giỏ hàng và tiến hành thanh toán. Chúng tôi hỗ trợ nhiều phương thức thanh toán tiện lợi.
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                            Thời gian giao hàng là bao lâu?
                        </button>
                    </h2>
                    <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Chúng tôi cam kết giao hàng trong vòng 30 phút đối với khu vực nội thành và 60 phút 
                            đối với khu vực ngoại thành. Thời gian có thể thay đổi tùy theo tình hình giao thông.
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                            Có chính sách đổi trả không?
                        </button>
                    </h2>
                    <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Chúng tôi có chính sách đổi trả trong vòng 24 giờ nếu sản phẩm có vấn đề về chất lượng. 
                            Vui lòng liên hệ hotline để được hỗ trợ nhanh chóng.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.getElementById('contactForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);
        
        // Simple validation
        if (!data.fullName || !data.email || !data.subject || !data.message || !data.agreeTerms) {
            alert('Vui lòng điền đầy đủ thông tin bắt buộc!');
            return;
        }
        
        // Simulate form submission
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang gửi...';
        submitBtn.disabled = true;
        
        setTimeout(() => {
            alert('Cảm ơn bạn đã liên hệ! Chúng tôi sẽ phản hồi trong thời gian sớm nhất.');
            this.reset();
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 2000);
    });
</script>
