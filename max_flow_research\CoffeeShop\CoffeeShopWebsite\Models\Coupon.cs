using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CoffeeShopWebsite.Models
{
    public enum CouponType
    {
        Percentage = 0,     // Giảm theo phần trăm
        FixedAmount = 1,    // <PERSON><PERSON><PERSON>m số tiền cố định
        FreeShipping = 2    // <PERSON><PERSON><PERSON> phí vận chuyển
    }

    public enum CouponStatus
    {
        Active = 0,         // Đang hoạt động
        Inactive = 1,       // Không hoạt động
        Expired = 2         // Đã hết hạn
    }

    public class Coupon
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Code { get; set; } = string.Empty;
        
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public CouponType Type { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal Value { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? MinOrderAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal? MaxDiscountAmount { get; set; }
        
        public int? UsageLimit { get; set; }
        
        public int UsedCount { get; set; } = 0;
        
        public DateTime StartDate { get; set; }
        
        public DateTime EndDate { get; set; }
        
        public CouponStatus Status { get; set; } = CouponStatus.Active;
        
        public bool IsForNewCustomersOnly { get; set; } = false;
        
        public string? RequiredCustomerLevel { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public string? CreatedBy { get; set; }
        
        // Navigation properties
        public virtual ICollection<UserCoupon> UserCoupons { get; set; } = new List<UserCoupon>();
        public virtual ICollection<Order> Orders { get; set; } = new List<Order>();
    }
}
