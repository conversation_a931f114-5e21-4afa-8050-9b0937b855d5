#include "graph.h"
#include <iostream>
#include <string>

using namespace std;

/**
 * Hiển thị menu chính
 */
void displayMenu() {
    cout << "\n" << string(50, '=') << "\n";
    cout << "    CHƯƠNG TRÌNH MINH HỌA LUỒNG CỰC ĐẠI\n";
    cout << "         (Ford-Fulkerson Algorithm)\n";
    cout << string(50, '=') << "\n";
    cout << "1. Test case 1 - Mạng đơn giản (4 đỉnh)\n";
    cout << "2. Test case 2 - Mạng phức tạp (6 đỉnh)\n";
    cout << "3. Test case 3 - Mạng tùy chỉnh\n";
    cout << "4. Nhập mạng luồng mới\n";
    cout << "5. Hướng dẫn sử dụng\n";
    cout << "0. Thoát\n";
    cout << string(50, '-') << "\n";
    cout << "Lựa chọn của bạn: ";
}

/**
 * Test case 1: Mạng đơn giản với 4 đỉnh
 */
void testCase1() {
    cout << "\n" << string(60, '=') << "\n";
    cout << "TEST CASE 1: MẠNG LUỒNG ĐỠN GIẢN (4 ĐỈNH)\n";
    cout << string(60, '=') << "\n";
    
    Graph g(4);
    
    // Thêm các cạnh: (từ, đến, dung_lượng)
    g.addEdge(0, 1, 16);  // s -> 1
    g.addEdge(0, 2, 13);  // s -> 2
    g.addEdge(1, 2, 10);  // 1 -> 2
    g.addEdge(1, 3, 12);  // 1 -> t
    g.addEdge(2, 1, 4);   // 2 -> 1
    g.addEdge(2, 3, 14);  // 2 -> t
    
    cout << "Cấu trúc mạng:\n";
    cout << "s(0) --16--> 1 --12--> t(3)\n";
    cout << " |           |          ^\n";
    cout << " 13         10         14\n";
    cout << " |           v          |\n";
    cout << " +-----> 2 --4--> 1 ----+\n";
    cout << "         |              \n";
    cout << "         +------14------>\n\n";
    
    g.printCapacity();
    
    int source = 0, sink = 3;
    int maxFlow = g.fordFulkerson(source, sink);
    
    g.printFlow();
    g.printFlowDetails();
    g.findMinCut(source);
}

/**
 * Test case 2: Mạng phức tạp với 6 đỉnh
 */
void testCase2() {
    cout << "\n" << string(60, '=') << "\n";
    cout << "TEST CASE 2: MẠNG LUỒNG PHỨC TẠP (6 ĐỈNH)\n";
    cout << string(60, '=') << "\n";
    
    Graph g(6);
    
    // Thêm các cạnh cho mạng phức tạp hơn
    g.addEdge(0, 1, 10);  // s -> 1
    g.addEdge(0, 2, 10);  // s -> 2
    g.addEdge(1, 2, 2);   // 1 -> 2
    g.addEdge(1, 3, 4);   // 1 -> 3
    g.addEdge(1, 4, 8);   // 1 -> 4
    g.addEdge(2, 4, 9);   // 2 -> 4
    g.addEdge(3, 5, 10);  // 3 -> t
    g.addEdge(4, 3, 6);   // 4 -> 3
    g.addEdge(4, 5, 10);  // 4 -> t
    
    cout << "Cấu trúc mạng phức tạp:\n";
    cout << "     1 --4--> 3\n";
    cout << "   / |        | \\\n";
    cout << "  10 2        6  10\n";
    cout << " /   |        |   \\\n";
    cout << "s    v        v    t\n";
    cout << " \\   4 --6--> 4   /\n";
    cout << "  10 |        |  10\n";
    cout << "   \\ v        | /\n";
    cout << "     2 --9----+\n\n";
    
    g.printCapacity();
    
    int source = 0, sink = 5;
    int maxFlow = g.fordFulkerson(source, sink);
    
    g.printFlow();
    g.printFlowDetails();
    g.findMinCut(source);
}

/**
 * Test case 3: Mạng tùy chỉnh
 */
void testCase3() {
    cout << "\n" << string(60, '=') << "\n";
    cout << "TEST CASE 3: MẠNG LUỒNG TÙY CHỈNH\n";
    cout << string(60, '=') << "\n";
    
    Graph g(5);
    
    // Mạng với nhiều đường đi song song
    g.addEdge(0, 1, 20);  // s -> 1
    g.addEdge(0, 2, 30);  // s -> 2
    g.addEdge(1, 2, 40);  // 1 -> 2
    g.addEdge(1, 3, 30);  // 1 -> 3
    g.addEdge(2, 3, 20);  // 2 -> 3
    g.addEdge(2, 4, 30);  // 2 -> t
    g.addEdge(3, 4, 20);  // 3 -> t
    
    cout << "Mạng với nhiều đường đi song song:\n";
    cout << "s --20--> 1 --30--> 3\n";
    cout << "|         |         |\n";
    cout << "30       40        20\n";
    cout << "|         v         v\n";
    cout << "+---> 2 --20--> 3 --+\n";
    cout << "      |             |\n";
    cout << "      +----30-----> t\n\n";
    
    g.printCapacity();
    
    int source = 0, sink = 4;
    int maxFlow = g.fordFulkerson(source, sink);
    
    g.printFlow();
    g.printFlowDetails();
    g.findMinCut(source);
}

/**
 * Nhập mạng luồng mới từ người dùng
 */
void inputCustomNetwork() {
    cout << "\n" << string(60, '=') << "\n";
    cout << "NHẬP MẠNG LUỒNG TÙY CHỈNH\n";
    cout << string(60, '=') << "\n";
    
    int vertices, edges;
    cout << "Nhập số đỉnh: ";
    cin >> vertices;
    
    if (vertices < 2) {
        cout << "Số đỉnh phải >= 2!\n";
        return;
    }
    
    Graph g(vertices);
    
    cout << "Nhập số cạnh: ";
    cin >> edges;
    
    cout << "\nNhập thông tin các cạnh (từ đến dung_lượng):\n";
    cout << "Lưu ý: Đỉnh được đánh số từ 0 đến " << vertices-1 << "\n";
    
    for (int i = 0; i < edges; i++) {
        int from, to, capacity;
        cout << "Cạnh " << i+1 << ": ";
        cin >> from >> to >> capacity;
        
        if (from >= 0 && from < vertices && to >= 0 && to < vertices && capacity > 0) {
            g.addEdge(from, to, capacity);
        } else {
            cout << "Thông tin cạnh không hợp lệ! Vui lòng nhập lại.\n";
            i--; // Nhập lại cạnh này
        }
    }
    
    int source, sink;
    cout << "\nNhập đỉnh nguồn (0-" << vertices-1 << "): ";
    cin >> source;
    cout << "Nhập đỉnh đích (0-" << vertices-1 << "): ";
    cin >> sink;
    
    if (source < 0 || source >= vertices || sink < 0 || sink >= vertices || source == sink) {
        cout << "Đỉnh nguồn hoặc đích không hợp lệ!\n";
        return;
    }
    
    g.printCapacity();
    
    int maxFlow = g.fordFulkerson(source, sink);
    
    g.printFlow();
    g.printFlowDetails();
    g.findMinCut(source);
}

/**
 * Hiển thị hướng dẫn sử dụng
 */
void showInstructions() {
    cout << "\n" << string(60, '=') << "\n";
    cout << "HƯỚNG DẪN SỬ DỤNG CHƯƠNG TRÌNH\n";
    cout << string(60, '=') << "\n";
    
    cout << "\n1. GIỚI THIỆU:\n";
    cout << "   - Chương trình minh họa thuật toán Ford-Fulkerson\n";
    cout << "   - Tìm luồng cực đại trong mạng luồng có hướng\n";
    cout << "   - Hiển thị chi tiết quá trình thực hiện\n";
    
    cout << "\n2. CÁC CHỨC NĂNG:\n";
    cout << "   - Test case có sẵn với các mạng mẫu\n";
    cout << "   - Nhập mạng tùy chỉnh\n";
    cout << "   - Hiển thị ma trận dung lượng và luồng\n";
    cout << "   - Tìm lát cắt tối thiểu\n";
    
    cout << "\n3. CÁCH SỬ DỤNG:\n";
    cout << "   - Chọn menu tương ứng\n";
    cout << "   - Với mạng tùy chỉnh: nhập số đỉnh, cạnh, và thông tin các cạnh\n";
    cout << "   - Đỉnh được đánh số từ 0\n";
    cout << "   - Dung lượng phải > 0\n";
    
    cout << "\n4. KẾT QUẢ HIỂN THỊ:\n";
    cout << "   - Quá trình tìm đường tăng luồng\n";
    cout << "   - Ma trận luồng kết quả\n";
    cout << "   - Chi tiết luồng trên từng cạnh\n";
    cout << "   - Lát cắt tối thiểu\n";
    
    cout << "\nNhấn Enter để tiếp tục...";
    cin.ignore();
    cin.get();
}

/**
 * Hàm main
 */
int main() {
    int choice;
    
    cout << "CHÀO MỪNG ĐẾN VỚI CHƯƠNG TRÌNH MINH HỌA THUẬT TOÁN FORD-FULKERSON!\n";
    cout << "Tác giả: [Tên sinh viên]\n";
    cout << "Môn học: Cấu trúc dữ liệu và Giải thuật\n";
    
    do {
        displayMenu();
        cin >> choice;
        
        switch (choice) {
            case 1:
                testCase1();
                break;
            case 2:
                testCase2();
                break;
            case 3:
                testCase3();
                break;
            case 4:
                inputCustomNetwork();
                break;
            case 5:
                showInstructions();
                break;
            case 0:
                cout << "\nCảm ơn bạn đã sử dụng chương trình!\n";
                cout << "Chúc bạn học tập tốt!\n";
                break;
            default:
                cout << "\nLựa chọn không hợp lệ! Vui lòng chọn lại.\n";
        }
        
        if (choice != 0 && choice != 5) {
            cout << "\nNhấn Enter để tiếp tục...";
            cin.ignore();
            cin.get();
        }
        
    } while (choice != 0);
    
    return 0;
}
