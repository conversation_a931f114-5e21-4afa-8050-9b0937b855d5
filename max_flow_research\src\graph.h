#ifndef GRAPH_H
#define GRAPH_H

#include <vector>
#include <queue>
#include <iostream>
#include <climits>
#include <iomanip>

using namespace std;

/**
 * Lớp Graph để biểu diễn mạng luồng
 * Sử dụng ma trận kề để lưu trữ dung lượng các cạnh
 */
class Graph {
private:
    int V; // Số đỉnh
    vector<vector<int>> capacity; // Ma trận dung lượng
    vector<vector<int>> flow;     // Ma trận luồng hiện tại
    
public:
    /**
     * Constructor khởi tạo đồ thị với V đỉnh
     * @param vertices: số lượng đỉnh
     */
    Graph(int vertices);
    
    /**
     * Thêm cạnh vào đồ thị
     * @param from: đỉnh xuất phát
     * @param to: đỉnh đích
     * @param cap: dung lượng cạnh
     */
    void addEdge(int from, int to, int cap);
    
    /**
     * Thuật toán BFS để tìm đường tăng luồng
     * @param source: đỉnh nguồn
     * @param sink: đỉnh đích
     * @param parent: mảng lưu đường đi
     * @return true nếu tìm thấy đường đi, false nếu không
     */
    bool bfs(int source, int sink, vector<int>& parent);
    
    /**
     * Thuật toán Ford-Fulkerson tìm luồng cực đại
     * @param source: đỉnh nguồn
     * @param sink: đỉnh đích
     * @return giá trị luồng cực đại
     */
    int fordFulkerson(int source, int sink);
    
    /**
     * In ma trận dung lượng
     */
    void printCapacity();
    
    /**
     * In ma trận luồng kết quả
     */
    void printFlow();
    
    /**
     * In thông tin chi tiết về luồng trên từng cạnh
     */
    void printFlowDetails();
    
    /**
     * Tìm và in lát cắt tối thiểu
     * @param source: đỉnh nguồn
     */
    void findMinCut(int source);
    
    /**
     * Reset luồng về 0 cho tất cả các cạnh
     */
    void resetFlow();
    
    /**
     * Lấy số đỉnh của đồ thị
     * @return số đỉnh
     */
    int getVertices() const { return V; }
    
    /**
     * Lấy dung lượng của cạnh (u,v)
     * @param u: đỉnh xuất phát
     * @param v: đỉnh đích
     * @return dung lượng cạnh
     */
    int getCapacity(int u, int v) const { return capacity[u][v]; }
    
    /**
     * Lấy luồng hiện tại của cạnh (u,v)
     * @param u: đỉnh xuất phát
     * @param v: đỉnh đích
     * @return luồng hiện tại
     */
    int getFlow(int u, int v) const { return flow[u][v]; }
};

#endif // GRAPH_H
