﻿<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Coffee Shop</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/CoffeeShopWebsite.styles.css" asp-append-version="true" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container">
                <a class="navbar-brand fw-bold" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-coffee me-2"></i>Coffee Shop
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home me-1"></i>Trang chủ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Products" asp-action="Index">
                                <i class="fas fa-list me-1"></i>Sản phẩm
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-controller="Products" asp-action="Featured">
                                <i class="fas fa-star me-1"></i>Nổi bật
                            </a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link position-relative" asp-controller="Cart" asp-action="Index">
                                <i class="fas fa-shopping-cart me-1"></i>Giỏ hàng
                                <span class="badge bg-danger rounded-pill position-absolute top-0 start-100 translate-middle" id="cart-count" style="display: none;">0</span>
                            </a>
                        </li>
                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user me-1"></i>@User.Identity.Name
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" asp-controller="Account" asp-action="Profile">
                                        <i class="fas fa-user-circle me-2"></i>Thông tin cá nhân
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="Order" asp-action="Index">
                                        <i class="fas fa-history me-2"></i>Lịch sử đơn hàng
                                    </a></li>
                                    <li><a class="dropdown-item" href="#">
                                        <i class="fas fa-gift me-2"></i>Mã giảm giá
                                    </a></li>
                                    @if (User.IsInRole("Admin"))
                                    {
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" asp-controller="Admin" asp-action="Index">
                                            <i class="fas fa-cogs me-2"></i>Quản trị
                                        </a></li>
                                    }
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item">
                                                <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Account" asp-action="Login">
                                    <i class="fas fa-sign-in-alt me-1"></i>Đăng nhập
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Account" asp-action="Register">
                                    <i class="fas fa-user-plus me-1"></i>Đăng ký
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-coffee me-2"></i>Coffee Shop</h5>
                    <p>Thưởng thức hương vị cà phê tuyệt vời cùng chúng tôi!</p>
                </div>
                <div class="col-md-6">
                    <h6>Liên hệ</h6>
                    <p><i class="fas fa-phone me-2"></i>0123 456 789</p>
                    <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
                    <p><i class="fas fa-map-marker-alt me-2"></i>123 Đường ABC, Quận XYZ, TP.HCM</p>
                </div>
            </div>
            <hr class="my-3">
            <div class="text-center">
                <small>&copy; 2025 Coffee Shop. Tất cả quyền được bảo lưu.</small>
            </div>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
