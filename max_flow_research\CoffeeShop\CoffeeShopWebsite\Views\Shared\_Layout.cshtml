<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Aroma Coffee House</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Dancing+Script:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/CoffeeShopWebsite.styles.css" asp-append-version="true" />
    <link rel="icon" type="image/x-icon" href="~/favicon.ico">

    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D2691E;
            --accent-color: #F4A460;
            --dark-color: #2C1810;
            --light-color: #F5F5DC;
            --warning-color: #FFC107;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
        }

        .top-bar {
            background: linear-gradient(135deg, var(--dark-color), var(--primary-color));
            font-size: 0.9rem;
        }

        .main-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .navbar-brand .brand-text h4 {
            color: var(--primary-color) !important;
        }

        .nav-link {
            color: var(--dark-color) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), var(--accent-color));
            border: none;
            font-weight: 500;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
            transform: translateY(-2px);
        }

        .dropdown-menu {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .social-links a {
            transition: all 0.3s ease;
        }

        .social-links a:hover {
            color: var(--warning-color) !important;
            transform: translateY(-2px);
        }

        .logo-container {
            animation: pulse 2s infinite;
        }

        @@keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .footer {
            background: linear-gradient(135deg, var(--dark-color), var(--primary-color));
            color: white;
        }

        .footer h5 {
            color: var(--warning-color);
            font-family: 'Dancing Script', cursive;
        }

        .footer a {
            color: #ccc;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .footer a:hover {
            color: var(--warning-color);
        }
    </style>
</head>
<body>
    <!-- Top Bar -->
    <div class="top-bar py-2 d-none d-md-block">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-phone me-2 text-warning"></i>
                        <span class="me-4">Hotline: 0123 456 789</span>
                        <i class="fas fa-envelope me-2 text-warning"></i>
                        <span><EMAIL></span>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-youtube"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <header class="main-header">
        <nav class="navbar navbar-expand-lg navbar-light sticky-top">
            <div class="container">
                <!-- Brand Logo -->
                <a class="navbar-brand d-flex align-items-center" asp-controller="Home" asp-action="Index">
                    <div class="logo-container me-3">
                        <i class="fas fa-coffee text-warning fs-2"></i>
                    </div>
                    <div class="brand-text">
                        <h4 class="mb-0 fw-bold" style="font-family: 'Dancing Script', cursive;">Aroma</h4>
                        <small class="text-muted" style="font-size: 0.8rem;">Coffee House</small>
                    </div>
                </a>

                <!-- Mobile Toggle -->
                <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Navigation Menu -->
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav mx-auto">
                        <li class="nav-item">
                            <a class="nav-link fw-500 px-3" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home me-1"></i>Trang chủ
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle fw-500 px-3" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-coffee me-1"></i>Sản phẩm
                            </a>
                            <ul class="dropdown-menu border-0 shadow">
                                <li><a class="dropdown-item py-2" asp-controller="Products" asp-action="Index">
                                    <i class="fas fa-th-large me-2 text-warning"></i>Tất cả sản phẩm
                                </a></li>
                                <li><a class="dropdown-item py-2" asp-controller="Products" asp-action="Featured">
                                    <i class="fas fa-star me-2 text-warning"></i>Sản phẩm nổi bật
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item py-2" asp-controller="Products" asp-action="Category" asp-route-id="1">
                                    <i class="fas fa-coffee me-2 text-warning"></i>Cà phê
                                </a></li>
                                <li><a class="dropdown-item py-2" asp-controller="Products" asp-action="Category" asp-route-id="2">
                                    <i class="fas fa-glass me-2 text-warning"></i>Trà sữa
                                </a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link fw-500 px-3" asp-controller="Home" asp-action="About">
                                <i class="fas fa-info-circle me-1"></i>Về chúng tôi
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link fw-500 px-3" asp-controller="Home" asp-action="Contact">
                                <i class="fas fa-phone me-1"></i>Liên hệ
                            </a>
                        </li>
                    </ul>

                    <!-- Right Side Menu -->
                    <ul class="navbar-nav">
                        <!-- Search -->
                        <li class="nav-item me-2">
                            <button class="btn btn-outline-warning btn-sm rounded-pill" type="button" data-bs-toggle="modal" data-bs-target="#searchModal">
                                <i class="fas fa-search"></i>
                            </button>
                        </li>

                        <!-- Cart -->
                        <li class="nav-item me-2">
                            <a class="btn btn-warning btn-sm rounded-pill position-relative px-3" asp-controller="Cart" asp-action="Index">
                                <i class="fas fa-shopping-cart me-1"></i>
                                <span class="d-none d-md-inline">Giỏ hàng</span>
                                <span class="badge bg-danger position-absolute top-0 start-100 translate-middle rounded-pill" id="cart-count" style="display: none;">
                                    0
                                </span>
                            </a>
                        </li>

                        <!-- User Menu -->
                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle fw-500" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user-circle me-1 text-warning"></i>
                                    <span class="d-none d-md-inline">@User.Identity.Name</span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end border-0 shadow">
                                    <li><a class="dropdown-item py-2" asp-controller="Order" asp-action="Index">
                                        <i class="fas fa-history me-2 text-warning"></i>Đơn hàng của tôi
                                    </a></li>
                                    <li><a class="dropdown-item py-2" href="#">
                                        <i class="fas fa-user-cog me-2 text-warning"></i>Thông tin cá nhân
                                    </a></li>
                                    <li><a class="dropdown-item py-2" href="#">
                                        <i class="fas fa-heart me-2 text-warning"></i>Yêu thích
                                    </a></li>
                                    @if (User.IsInRole("Admin"))
                                    {
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item py-2" asp-controller="Admin" asp-action="Index">
                                            <i class="fas fa-cogs me-2 text-warning"></i>Quản trị
                                        </a></li>
                                    }
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item py-2 text-danger">
                                                <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="btn btn-outline-dark btn-sm rounded-pill me-2" asp-controller="Account" asp-action="Login">
                                    <i class="fas fa-sign-in-alt me-1"></i>Đăng nhập
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="btn btn-dark btn-sm rounded-pill" asp-controller="Account" asp-action="Register">
                                    <i class="fas fa-user-plus me-1"></i>Đăng ký
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <!-- Search Modal -->
    <div class="modal fade" id="searchModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title">Tìm kiếm sản phẩm</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form asp-controller="Products" asp-action="Index" method="get">
                        <div class="input-group">
                            <input type="text" name="searchString" class="form-control" placeholder="Nhập tên sản phẩm..." autofocus>
                            <button class="btn btn-warning" type="submit">
                                <i class="fas fa-search"></i> Tìm kiếm
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main role="main" class="flex-grow-1">
        @RenderBody()
    </main>

    <!-- Footer -->
    <footer class="footer mt-5 py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-coffee text-warning fs-2 me-3"></i>
                        <div>
                            <h5 class="mb-0" style="font-family: 'Dancing Script', cursive;">Aroma Coffee House</h5>
                            <small class="text-muted">Hương vị đậm đà</small>
                        </div>
                    </div>
                    <p class="text-light">Chúng tôi mang đến những ly cà phê tuyệt vời nhất với hương vị đậm đà và không gian ấm cúng.</p>
                    <div class="social-links">
                        <a href="#" class="me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="me-3"><i class="fab fa-youtube"></i></a>
                        <a href="#"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="text-warning mb-3">Sản phẩm</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a asp-controller="Products" asp-action="Index">Tất cả sản phẩm</a></li>
                        <li class="mb-2"><a asp-controller="Products" asp-action="Featured">Sản phẩm nổi bật</a></li>
                        <li class="mb-2"><a asp-controller="Products" asp-action="Category" asp-route-id="1">Cà phê</a></li>
                        <li class="mb-2"><a asp-controller="Products" asp-action="Category" asp-route-id="2">Trà sữa</a></li>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="text-warning mb-3">Thông tin</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a asp-controller="Home" asp-action="About">Về chúng tôi</a></li>
                        <li class="mb-2"><a asp-controller="Home" asp-action="Contact">Liên hệ</a></li>
                        <li class="mb-2"><a href="#">Chính sách bảo mật</a></li>
                        <li class="mb-2"><a href="#">Điều khoản sử dụng</a></li>
                    </ul>
                </div>

                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="text-warning mb-3">Liên hệ</h6>
                    <div class="contact-info">
                        <p class="mb-2">
                            <i class="fas fa-map-marker-alt me-2 text-warning"></i>
                            123 Đường Nguyễn Huệ, Q.1, TP.HCM
                        </p>
                        <p class="mb-2">
                            <i class="fas fa-phone me-2 text-warning"></i>
                            <a href="tel:0123456789">0123 456 789</a>
                        </p>
                        <p class="mb-2">
                            <i class="fas fa-envelope me-2 text-warning"></i>
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </p>
                        <p class="mb-0">
                            <i class="fas fa-clock me-2 text-warning"></i>
                            Mở cửa: 6:00 - 22:00 hàng ngày
                        </p>
                    </div>
                </div>
            </div>

            <hr class="my-4 border-secondary">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">&copy; 2025 Aroma Coffee House. Tất cả quyền được bảo lưu.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">
                        Thiết kế bởi <span class="text-warning">Aroma Team</span>
                    </p>
                </div>
            </div>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
