@model CoffeeShopWebsite.ViewModels.OrderListViewModel
@{
    ViewData["Title"] = "Lịch sử đơn hàng";
}

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-history me-2"></i>Lịch sử đơn hàng</h2>
        <a asp-controller="Products" asp-action="Index" class="btn btn-primary">
            <i class="fas fa-shopping-bag me-1"></i>Tiếp tục mua sắm
        </a>
    </div>

    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show">
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            @TempData["SuccessMessage"]
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show">
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            @TempData["ErrorMessage"]
        </div>
    }

    @if (Model.Orders.Any())
    {
        <div class="row">
            @foreach (var order in Model.Orders)
            {
                <div class="col-12 mb-3">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col-md-3">
                                    <strong>Đơn hàng #@order.OrderNumber</strong>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">@order.OrderDate.ToString("dd/MM/yyyy HH:mm")</small>
                                </div>
                                <div class="col-md-3">
                                    @{
                                        var statusClass = order.Status switch
                                        {
                                            OrderStatus.Pending => "bg-warning",
                                            OrderStatus.Confirmed => "bg-info",
                                            OrderStatus.Preparing => "bg-primary",
                                            OrderStatus.Ready => "bg-success",
                                            OrderStatus.Delivered => "bg-success",
                                            OrderStatus.Cancelled => "bg-danger",
                                            _ => "bg-secondary"
                                        };
                                        var statusText = order.Status switch
                                        {
                                            OrderStatus.Pending => "Chờ xử lý",
                                            OrderStatus.Confirmed => "Đã xác nhận",
                                            OrderStatus.Preparing => "Đang chuẩn bị",
                                            OrderStatus.Ready => "Sẵn sàng",
                                            OrderStatus.Delivered => "Đã giao hàng",
                                            OrderStatus.Cancelled => "Đã hủy",
                                            _ => order.Status.ToString()
                                        };
                                    }
                                    <span class="badge @statusClass">@statusText</span>
                                </div>
                                <div class="col-md-3 text-end">
                                    <strong class="text-primary">@order.TotalAmount.ToString("N0") VNĐ</strong>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="d-flex flex-wrap">
                                        @foreach (var item in order.OrderItems.Take(3))
                                        {
                                            <div class="d-flex align-items-center me-3 mb-2">
                                                @if (!string.IsNullOrEmpty(item.Product.ImageUrl))
                                                {
                                                    <img src="@item.Product.ImageUrl" class="me-2 rounded" style="width: 40px; height: 40px; object-fit: cover;" alt="@item.Product.Name">
                                                }
                                                <div>
                                                    <small class="fw-bold">@item.Product.Name</small>
                                                    <br>
                                                    <small class="text-muted"><EMAIL></small>
                                                </div>
                                            </div>
                                        }
                                        @if (order.OrderItems.Count() > 3)
                                        {
                                            <small class="text-muted align-self-center">và @(order.OrderItems.Count() - 3) sản phẩm khác</small>
                                        }
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@order.Id" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye me-1"></i>Chi tiết
                                        </a>
                                        @if (order.Status == OrderStatus.Pending)
                                        {
                                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="cancelOrder(@order.Id)">
                                                <i class="fas fa-times me-1"></i>Hủy
                                            </button>
                                        }
                                        @if (order.Status == OrderStatus.Delivered)
                                        {
                                            <a asp-action="Reorder" asp-route-id="@order.Id" class="btn btn-outline-success btn-sm">
                                                <i class="fas fa-redo me-1"></i>Đặt lại
                                            </a>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Pagination -->
        @if (Model.TotalPages > 1)
        {
            <nav aria-label="Order pagination" class="mt-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <small class="text-muted">
                            Hiển thị @Model.StartItem - @Model.EndItem trong tổng số @Model.TotalItems đơn hàng
                        </small>
                    </div>
                    <div>
                        <small class="text-muted">Trang @Model.CurrentPage / @Model.TotalPages</small>
                    </div>
                </div>
                
                <ul class="pagination justify-content-center">
                    @if (Model.HasPreviousPage)
                    {
                        <li class="page-item">
                            <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage - 1 })">
                                <i class="fas fa-angle-left"></i>
                            </a>
                        </li>
                    }

                    @{
                        int startPage = Math.Max(1, Model.CurrentPage - 2);
                        int endPage = Math.Min(Model.TotalPages, Model.CurrentPage + 2);
                    }

                    @for (int i = startPage; i <= endPage; i++)
                    {
                        <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                            <a class="page-link" href="@Url.Action("Index", new { page = i })">@i</a>
                        </li>
                    }

                    @if (Model.HasNextPage)
                    {
                        <li class="page-item">
                            <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage + 1 })">
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>
                    }
                </ul>
            </nav>
        }
    }
    else
    {
        <div class="text-center py-5">
            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
            <h4>Bạn chưa có đơn hàng nào</h4>
            <p class="text-muted">Hãy bắt đầu mua sắm để tạo đơn hàng đầu tiên</p>
            <a asp-controller="Products" asp-action="Index" class="btn btn-primary">
                <i class="fas fa-shopping-bag me-1"></i>Bắt đầu mua sắm
            </a>
        </div>
    }
</div>

@section Scripts {
    <script>
        function cancelOrder(orderId) {
            if (confirm('Bạn có chắc chắn muốn hủy đơn hàng này?')) {
                $.ajax({
                    url: '@Url.Action("Cancel")',
                    type: 'POST',
                    data: {
                        id: orderId,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        location.reload();
                    },
                    error: function() {
                        alert('Có lỗi xảy ra. Vui lòng thử lại.');
                    }
                });
            }
        }
    </script>
}
