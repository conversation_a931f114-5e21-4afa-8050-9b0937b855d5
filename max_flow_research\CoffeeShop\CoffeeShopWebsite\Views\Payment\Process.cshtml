@model CoffeeShopWebsite.ViewModels.PaymentViewModel
@{
    ViewData["Title"] = "Thanh toán";
}

<div class="container">
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-credit-card me-2"></i><PERSON><PERSON>n phương thức thanh toán</h4>
                </div>
                <div class="card-body">
                    @if (!ViewData.ModelState.IsValid)
                    {
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                                {
                                    <li>@error.ErrorMessage</li>
                                }
                            </ul>
                        </div>
                    }

                    <form asp-action="Process" method="post" id="paymentForm">
                        <input type="hidden" asp-for="Order.Id" />
                        
                        <!-- Payment Method Selection -->
                        <div class="mb-4">
                            <h5><PERSON><PERSON><PERSON><PERSON> thức thanh toán</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="card payment-method" data-method="Cash">
                                        <div class="card-body text-center">
                                            <input type="radio" name="Order.PaymentMethod" value="Cash" id="cash" checked>
                                            <label for="cash" class="w-100">
                                                <i class="fas fa-money-bill-wave fa-2x text-success mb-2"></i>
                                                <h6>Thanh toán khi nhận hàng</h6>
                                                <small class="text-muted">Thanh toán bằng tiền mặt khi nhận hàng</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card payment-method" data-method="BankTransfer">
                                        <div class="card-body text-center">
                                            <input type="radio" name="Order.PaymentMethod" value="BankTransfer" id="bank">
                                            <label for="bank" class="w-100">
                                                <i class="fas fa-university fa-2x text-primary mb-2"></i>
                                                <h6>Chuyển khoản ngân hàng</h6>
                                                <small class="text-muted">Chuyển khoản qua ngân hàng</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card payment-method" data-method="CreditCard">
                                        <div class="card-body text-center">
                                            <input type="radio" name="Order.PaymentMethod" value="CreditCard" id="card">
                                            <label for="card" class="w-100">
                                                <i class="fas fa-credit-card fa-2x text-warning mb-2"></i>
                                                <h6>Thẻ tín dụng/ghi nợ</h6>
                                                <small class="text-muted">Visa, MasterCard</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="card payment-method" data-method="EWallet">
                                        <div class="card-body text-center">
                                            <input type="radio" name="Order.PaymentMethod" value="EWallet" id="ewallet">
                                            <label for="ewallet" class="w-100">
                                                <i class="fas fa-mobile-alt fa-2x text-info mb-2"></i>
                                                <h6>Ví điện tử</h6>
                                                <small class="text-muted">MoMo, ZaloPay, VNPay</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Bank Transfer Details -->
                        <div id="bankTransferDetails" class="payment-details" style="display: none;">
                            <h6>Thông tin chuyển khoản</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Tên ngân hàng</label>
                                        <input asp-for="PaymentRequest.BankTransfer.BankName" class="form-control" placeholder="VD: Vietcombank">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Số tài khoản</label>
                                        <input asp-for="PaymentRequest.BankTransfer.AccountNumber" class="form-control" placeholder="Số tài khoản của bạn">
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label">Tên chủ tài khoản</label>
                                        <input asp-for="PaymentRequest.BankTransfer.AccountName" class="form-control" placeholder="Tên chủ tài khoản">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Credit Card Details -->
                        <div id="creditCardDetails" class="payment-details" style="display: none;">
                            <h6>Thông tin thẻ tín dụng</h6>
                            <div class="row">
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label">Số thẻ</label>
                                        <input asp-for="PaymentRequest.CreditCard.CardNumber" class="form-control" placeholder="1234 5678 9012 3456" maxlength="19">
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label">Tên chủ thẻ</label>
                                        <input asp-for="PaymentRequest.CreditCard.CardHolderName" class="form-control" placeholder="NGUYEN VAN A">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Tháng hết hạn</label>
                                        <select asp-for="PaymentRequest.CreditCard.ExpiryMonth" class="form-select">
                                            <option value="">Tháng</option>
                                            @for (int i = 1; i <= 12; i++)
                                            {
                                                <option value="@i">@i.ToString("00")</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Năm hết hạn</label>
                                        <select asp-for="PaymentRequest.CreditCard.ExpiryYear" class="form-select">
                                            <option value="">Năm</option>
                                            @for (int i = DateTime.Now.Year; i <= DateTime.Now.Year + 10; i++)
                                            {
                                                <option value="@i">@i</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">CVV</label>
                                        <input asp-for="PaymentRequest.CreditCard.CVV" class="form-control" placeholder="123" maxlength="4">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- E-Wallet Details -->
                        <div id="ewalletDetails" class="payment-details" style="display: none;">
                            <h6>Chọn ví điện tử</h6>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="card ewallet-option">
                                        <div class="card-body text-center">
                                            <input type="radio" name="PaymentRequest.EWallet.WalletType" value="momo" id="momo">
                                            <label for="momo" class="w-100">
                                                <img src="https://developers.momo.vn/v3/assets/images/square-logo.svg" alt="MoMo" style="height: 40px;" class="mb-2">
                                                <div>MoMo</div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="card ewallet-option">
                                        <div class="card-body text-center">
                                            <input type="radio" name="PaymentRequest.EWallet.WalletType" value="zalopay" id="zalopay">
                                            <label for="zalopay" class="w-100">
                                                <img src="https://cdn.haitrieu.com/wp-content/uploads/2022/10/Logo-ZaloPay-Square.png" alt="ZaloPay" style="height: 40px;" class="mb-2">
                                                <div>ZaloPay</div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="card ewallet-option">
                                        <div class="card-body text-center">
                                            <input type="radio" name="PaymentRequest.EWallet.WalletType" value="vnpay" id="vnpay">
                                            <label for="vnpay" class="w-100">
                                                <img src="https://vnpay.vn/s1/statics.vnpay.vn/2023/9/06ncktiwd6dc1694418196384.png" alt="VNPay" style="height: 40px;" class="mb-2">
                                                <div>VNPay</div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a asp-controller="Cart" asp-action="Checkout" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Quay lại
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-credit-card me-1"></i>Thanh toán @Model.Order.TotalAmount.ToString("N0") VNĐ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-receipt me-2"></i>Thông tin đơn hàng</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Mã đơn hàng:</strong> @Model.Order.OrderNumber
                    </div>
                    <div class="mb-3">
                        <strong>Khách hàng:</strong> @Model.Order.CustomerName
                    </div>
                    <div class="mb-3">
                        <strong>Điện thoại:</strong> @Model.Order.CustomerPhone
                    </div>
                    <div class="mb-3">
                        <strong>Email:</strong> @Model.Order.CustomerEmail
                    </div>
                    @if (!string.IsNullOrEmpty(Model.Order.DeliveryAddress))
                    {
                        <div class="mb-3">
                            <strong>Địa chỉ giao hàng:</strong><br>
                            @Model.Order.DeliveryAddress
                        </div>
                    }
                    <hr>
                    <div class="d-flex justify-content-between">
                        <strong>Tổng cộng:</strong>
                        <strong class="text-primary">@Model.Order.TotalAmount.ToString("N0") VNĐ</strong>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="fas fa-shield-alt me-2"></i>Bảo mật thanh toán</h6>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <i class="fas fa-lock me-1"></i>Thông tin thanh toán của bạn được mã hóa và bảo mật tuyệt đối.<br>
                        <i class="fas fa-check me-1"></i>Chúng tôi không lưu trữ thông tin thẻ tín dụng.<br>
                        <i class="fas fa-phone me-1"></i>Hỗ trợ 24/7: 0123 456 789
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize first payment method
            $('input[name="Order.PaymentMethod"]:checked').closest('.payment-method').addClass('border-primary');

            // Payment method selection
            $('input[name="Order.PaymentMethod"]').change(function() {
                var method = $(this).val();
                $('.payment-details').hide();
                $('.payment-method').removeClass('border-primary');

                $(this).closest('.payment-method').addClass('border-primary');

                if (method === 'BankTransfer') {
                    $('#bankTransferDetails').show();
                } else if (method === 'CreditCard') {
                    $('#creditCardDetails').show();
                } else if (method === 'EWallet') {
                    $('#ewalletDetails').show();
                }
            });

            // Credit card number formatting
            $('input[name="PaymentRequest.CreditCard.CardNumber"]').on('input', function() {
                var value = $(this).val().replace(/\s/g, '').replace(/[^0-9]/gi, '');
                var formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
                $(this).val(formattedValue);
            });

            // E-wallet option selection
            $('.ewallet-option input[type="radio"]').change(function() {
                $('.ewallet-option').removeClass('border-primary');
                $(this).closest('.ewallet-option').addClass('border-primary');
            });

            // Form validation
            $('#paymentForm').submit(function(e) {
                var method = $('input[name="Order.PaymentMethod"]:checked').val();

                if (method === 'CreditCard') {
                    var cardNumber = $('input[name="PaymentRequest.CreditCard.CardNumber"]').val().replace(/\s/g, '');
                    if (cardNumber.length < 16) {
                        e.preventDefault();
                        alert('Vui lòng nhập đầy đủ số thẻ');
                        return false;
                    }
                }

                if (method === 'EWallet') {
                    var walletType = $('input[name="PaymentRequest.EWallet.WalletType"]:checked').val();
                    if (!walletType) {
                        e.preventDefault();
                        alert('Vui lòng chọn ví điện tử');
                        return false;
                    }
                }
            });
        });
    </script>

    <style>
        .payment-method {
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .payment-method:hover {
            border-color: #007bff !important;
        }
        
        .payment-method input[type="radio"] {
            display: none;
        }
        
        .payment-method label {
            cursor: pointer;
            margin: 0;
        }
        
        .ewallet-option {
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .ewallet-option:hover {
            border-color: #007bff !important;
        }
        
        .ewallet-option input[type="radio"] {
            display: none;
        }
        
        .ewallet-option label {
            cursor: pointer;
            margin: 0;
        }
    </style>
}
