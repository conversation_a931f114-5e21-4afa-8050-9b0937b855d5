using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CoffeeShopWebsite.Models
{
    public enum OrderStatus
    {
        Pending = 0,        // Chờ xử lý
        Confirmed = 1,      // Đã xác nhận
        Preparing = 2,      // <PERSON>ang chuẩn bị
        Ready = 3,          // Sẵn sàng
        Delivered = 4,      // Đã giao
        Cancelled = 5       // Đã hủy
    }

    public enum PaymentMethod
    {
        Cash = 0,           // Tiền mặt
        BankTransfer = 1,   // Chuyển khoản
        CreditCard = 2,     // Thẻ tín dụng
        EWallet = 3         // Ví điện tử
    }

    public class Order
    {
        public int Id { get; set; }
        
        [Required]
        public string OrderNumber { get; set; } = string.Empty;
        
        public DateTime OrderDate { get; set; } = DateTime.Now;
        
        [Required(ErrorMessage = "Tổng tiền là bắt buộc")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }
        
        public OrderStatus Status { get; set; } = OrderStatus.Pending;
        
        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Cash;
        
        public bool IsPaid { get; set; } = false;
        
        [StringLength(500, ErrorMessage = "Ghi chú không được vượt quá 500 ký tự")]
        public string? Notes { get; set; }
        
        [Required(ErrorMessage = "Tên khách hàng là bắt buộc")]
        [StringLength(100, ErrorMessage = "Tên khách hàng không được vượt quá 100 ký tự")]
        public string CustomerName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email là bắt buộc")]
        [EmailAddress(ErrorMessage = "Email không hợp lệ")]
        [StringLength(150, ErrorMessage = "Email không được vượt quá 150 ký tự")]
        public string CustomerEmail { get; set; } = string.Empty;

        [Required(ErrorMessage = "Số điện thoại là bắt buộc")]
        [Phone(ErrorMessage = "Số điện thoại không hợp lệ")]
        public string CustomerPhone { get; set; } = string.Empty;
        
        [StringLength(300, ErrorMessage = "Địa chỉ không được vượt quá 300 ký tự")]
        public string? DeliveryAddress { get; set; }
        
        public DateTime? DeliveryTime { get; set; }
        
        // Foreign key (nullable for guest orders)
        public int? CustomerId { get; set; }

        // Identity User (for registered users)
        public string? UserId { get; set; }

        // Coupon applied
        public int? CouponId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;

        // Navigation properties
        public virtual Customer? Customer { get; set; }
        public virtual ApplicationUser? User { get; set; }
        public virtual Coupon? Coupon { get; set; }
        public virtual ICollection<OrderItem> OrderItems { get; set; } = new List<OrderItem>();
    }
}
