@echo off
REM Build script cho dự án Max Flow Research
REM Tác giả: [Tên sinh viên]

echo ========================================
echo    BUILD SCRIPT - MAX FLOW RESEARCH
echo ========================================

REM Tạo thư mục build và bin nếu chưa có
if not exist "build" mkdir build
if not exist "bin" mkdir bin

echo.
echo [1/3] Kiểm tra compiler...

REM Kiểm tra g++ có sẵn không
g++ --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: g++ không được tìm thấy!
    echo Vui lòng cài đặt MinGW hoặc Dev-C++
    echo Hoặc sử dụng Visual Studio Developer Command Prompt
    pause
    exit /b 1
)

echo OK: g++ compiler đ<PERSON><PERSON><PERSON> tìm thấy

echo.
echo [2/3] Biên dịch mã nguồn...

REM Biên dịch từng file object
g++ -std=c++11 -Wall -O2 -c src/main.cpp -o build/main.o
if %errorlevel% neq 0 (
    echo ERROR: Lỗi biên dịch main.cpp
    pause
    exit /b 1
)

g++ -std=c++11 -Wall -O2 -c src/max_flow.cpp -o build/max_flow.o
if %errorlevel% neq 0 (
    echo ERROR: Lỗi biên dịch max_flow.cpp
    pause
    exit /b 1
)

echo OK: Biên dịch thành công các file object

echo.
echo [3/3] Liên kết tạo file thực thi...

REM Liên kết tạo file thực thi
g++ -std=c++11 -Wall -O2 build/main.o build/max_flow.o -o bin/max_flow.exe
if %errorlevel% neq 0 (
    echo ERROR: Lỗi liên kết
    pause
    exit /b 1
)

echo OK: Tạo file thực thi thành công

echo.
echo ========================================
echo    BUILD HOÀN THÀNH THÀNH CÔNG!
echo ========================================
echo.
echo File thực thi: bin/max_flow.exe
echo Để chạy chương trình: bin\max_flow.exe
echo Hoặc: run.bat
echo.

pause
