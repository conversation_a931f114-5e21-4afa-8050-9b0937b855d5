@model CoffeeShopWebsite.Models.Order
@{
    ViewData["Title"] = "Thanh toán";
    var cartItems = ViewBag.CartItems as List<CoffeeShopWebsite.Models.CartItem>;
    var cartTotal = (decimal)ViewBag.CartTotal;
}

<div class="container">
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-credit-card me-2"></i>Thông tin thanh toán</h4>
                </div>
                <div class="card-body">
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show">
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            @TempData["ErrorMessage"]
                        </div>
                    }

                    <form asp-action="Checkout" method="post">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>Thông tin khách hàng</h5>
                                <div class="mb-3">
                                    <label asp-for="CustomerName" class="form-label">Họ và tên *</label>
                                    <input asp-for="CustomerName" class="form-control" required>
                                    <span asp-validation-for="CustomerName" class="text-danger"></span>
                                </div>
                                <div class="mb-3">
                                    <label asp-for="CustomerEmail" class="form-label">Email *</label>
                                    <input asp-for="CustomerEmail" type="email" class="form-control" required>
                                    <span asp-validation-for="CustomerEmail" class="text-danger"></span>
                                </div>
                                <div class="mb-3">
                                    <label asp-for="CustomerPhone" class="form-label">Số điện thoại *</label>
                                    <input asp-for="CustomerPhone" class="form-control" required>
                                    <span asp-validation-for="CustomerPhone" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5>Địa chỉ giao hàng</h5>
                                <div class="mb-3">
                                    <label asp-for="DeliveryAddress" class="form-label">Địa chỉ *</label>
                                    <textarea asp-for="DeliveryAddress" class="form-control" rows="3" required></textarea>
                                    <span asp-validation-for="DeliveryAddress" class="text-danger"></span>
                                </div>
                                <div class="mb-3">
                                    <label asp-for="PaymentMethod" class="form-label">Phương thức thanh toán</label>
                                    <select asp-for="PaymentMethod" class="form-select">
                                        <option value="Cash">Thanh toán khi nhận hàng (COD)</option>
                                        <option value="BankTransfer">Chuyển khoản ngân hàng</option>
                                        <option value="CreditCard">Thẻ tín dụng</option>
                                        <option value="EWallet">Ví điện tử</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="Notes" class="form-label">Ghi chú đơn hàng</label>
                            <textarea asp-for="Notes" class="form-control" rows="2" placeholder="Ghi chú thêm về đơn hàng (tùy chọn)"></textarea>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Quay lại giỏ hàng
                            </a>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-check me-1"></i>Đặt hàng
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-shopping-cart me-2"></i>Đơn hàng của bạn</h5>
                </div>
                <div class="card-body">
                    @if (cartItems != null && cartItems.Any())
                    {
                        @foreach (var item in cartItems)
                        {
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="d-flex align-items-center">
                                    @if (!string.IsNullOrEmpty(item.Product.ImageUrl))
                                    {
                                        <img src="@item.Product.ImageUrl" class="me-2 rounded" style="width: 40px; height: 40px; object-fit: cover;" alt="@item.Product.Name">
                                    }
                                    <div>
                                        <small class="fw-bold">@item.Product.Name</small>
                                        <br>
                                        <small class="text-muted"><EMAIL></small>
                                    </div>
                                </div>
                                <small class="fw-bold">@item.TotalPrice.ToString("N0") VNĐ</small>
                            </div>
                        }
                        <hr>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tạm tính:</span>
                            <span>@cartTotal.ToString("N0") VNĐ</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Phí vận chuyển:</span>
                            <span class="text-success">Miễn phí</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between">
                            <strong>Tổng cộng:</strong>
                            <strong class="text-primary">@cartTotal.ToString("N0") VNĐ</strong>
                        </div>
                    }
                </div>
            </div>

            <!-- Payment Methods Info -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle me-2"></i>Thông tin thanh toán</h6>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <strong>COD:</strong> Thanh toán khi nhận hàng<br>
                        <strong>Chuyển khoản:</strong> Chuyển khoản trước khi giao hàng<br>
                        <strong>Thẻ tín dụng:</strong> Thanh toán online an toàn<br>
                        <strong>Ví điện tử:</strong> MoMo, ZaloPay, VNPay
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
