@model CoffeeShopWebsite.ViewModels.RegisterViewModel
@{
    ViewData["Title"] = "Đăng ký";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4><i class="fas fa-user-plus me-2"></i>Đăng ký tài khoản</h4>
                    <p class="mb-0">Tham gia Coffee Shop để nhận ưu đãi đặc biệt!</p>
                </div>
                <div class="card-body">
                    <form asp-action="Register" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="mb-3">
                            <label asp-for="FullName" class="form-label"></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                <input asp-for="FullName" class="form-control" placeholder="Nhập họ và tên" />
                            </div>
                            <span asp-validation-for="FullName" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Email" class="form-label"></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input asp-for="Email" class="form-control" placeholder="Nhập email" />
                            </div>
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="PhoneNumber" class="form-label"></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                <input asp-for="PhoneNumber" class="form-control" placeholder="Nhập số điện thoại" />
                            </div>
                            <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Address" class="form-label"></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                <textarea asp-for="Address" class="form-control" rows="2" placeholder="Nhập địa chỉ (tùy chọn)"></textarea>
                            </div>
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Password" class="form-label"></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input asp-for="Password" class="form-control" placeholder="Nhập mật khẩu" />
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="ConfirmPassword" class="form-label"></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input asp-for="ConfirmPassword" class="form-control" placeholder="Nhập lại mật khẩu" />
                            </div>
                            <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                        </div>

                        <div class="mb-3 form-check">
                            <input asp-for="AgreeToTerms" class="form-check-input" />
                            <label asp-for="AgreeToTerms" class="form-check-label">
                                Tôi đồng ý với <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">điều khoản sử dụng</a>
                            </label>
                            <span asp-validation-for="AgreeToTerms" class="text-danger d-block"></span>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>Đăng ký
                            </button>
                        </div>
                    </form>

                    <div class="text-center mt-3">
                        <p>Đã có tài khoản? <a asp-action="Login">Đăng nhập ngay</a></p>
                    </div>

                    <!-- Benefits Section -->
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6><i class="fas fa-gift me-2 text-primary"></i>Ưu đãi khi đăng ký:</h6>
                        <ul class="list-unstyled mb-0">
                            <li><i class="fas fa-check text-success me-2"></i>Nhận 100 điểm thưởng</li>
                            <li><i class="fas fa-check text-success me-2"></i>Mã giảm giá 10% đơn đầu tiên</li>
                            <li><i class="fas fa-check text-success me-2"></i>Ưu đãi độc quyền cho thành viên</li>
                            <li><i class="fas fa-check text-success me-2"></i>Tích điểm mỗi lần mua hàng</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Terms Modal -->
<div class="modal fade" id="termsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Điều khoản sử dụng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>1. Chấp nhận điều khoản</h6>
                <p>Bằng việc đăng ký tài khoản, bạn đồng ý tuân thủ các điều khoản và điều kiện sử dụng dịch vụ của Coffee Shop.</p>
                
                <h6>2. Thông tin tài khoản</h6>
                <p>Bạn cam kết cung cấp thông tin chính xác và cập nhật thông tin khi có thay đổi.</p>
                
                <h6>3. Bảo mật</h6>
                <p>Bạn có trách nhiệm bảo mật thông tin đăng nhập và thông báo ngay cho chúng tôi nếu phát hiện tài khoản bị sử dụng trái phép.</p>
                
                <h6>4. Chương trình khuyến mãi</h6>
                <p>Các chương trình khuyến mãi có thể thay đổi mà không cần thông báo trước. Điều khoản cụ thể của từng chương trình sẽ được công bố riêng.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            // Toggle password visibility
            $('#togglePassword').click(function() {
                var passwordField = $('#Password');
                var icon = $(this).find('i');
                
                if (passwordField.attr('type') === 'password') {
                    passwordField.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    passwordField.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });
        });
    </script>
}
