using CoffeeShopWebsite.Models;
using System.ComponentModel.DataAnnotations;

namespace CoffeeShopWebsite.ViewModels
{
    public enum PaymentStatus
    {
        Pending = 0,
        Processing = 1,
        Completed = 2,
        Failed = 3,
        Cancelled = 4,
        Refunded = 5
    }

    public class PaymentRequest
    {
        public BankTransferRequest? BankTransfer { get; set; }
        public CreditCardRequest? CreditCard { get; set; }
        public EWalletRequest? EWallet { get; set; }
    }

    public class BankTransferRequest
    {
        [Required(ErrorMessage = "Tên ngân hàng là bắt buộc")]
        public string BankName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Số tài khoản là bắt buộc")]
        public string AccountNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "Tên chủ tài khoản là bắt buộc")]
        public string AccountName { get; set; } = string.Empty;
    }

    public class CreditCardRequest
    {
        [Required(ErrorMessage = "Số thẻ là bắt buộc")]
        [StringLength(19, MinimumLength = 16, ErrorMessage = "Số thẻ không hợp lệ")]
        public string CardNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "Tên chủ thẻ là bắt buộc")]
        public string CardHolderName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Tháng hết hạn là bắt buộc")]
        [Range(1, 12, ErrorMessage = "Tháng hết hạn không hợp lệ")]
        public int ExpiryMonth { get; set; }

        [Required(ErrorMessage = "Năm hết hạn là bắt buộc")]
        [Range(2024, 2040, ErrorMessage = "Năm hết hạn không hợp lệ")]
        public int ExpiryYear { get; set; }

        [Required(ErrorMessage = "Mã CVV là bắt buộc")]
        [StringLength(4, MinimumLength = 3, ErrorMessage = "Mã CVV không hợp lệ")]
        public string CVV { get; set; } = string.Empty;
    }

    public class EWalletRequest
    {
        [Required(ErrorMessage = "Loại ví điện tử là bắt buộc")]
        public string WalletType { get; set; } = string.Empty; // momo, zalopay, vnpay

        public string? PhoneNumber { get; set; }
    }

    public class PaymentResult
    {
        public bool IsSuccess { get; set; }
        public string? TransactionId { get; set; }
        public PaymentMethod PaymentMethod { get; set; }
        public decimal Amount { get; set; }
        public PaymentStatus Status { get; set; }
        public string? Message { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime ProcessedAt { get; set; } = DateTime.Now;

        // Additional info for specific payment methods
        public BankInfo? BankInfo { get; set; }
        public EWalletInfo? EWalletInfo { get; set; }
        public string? QRCodeUrl { get; set; }
    }

    public class BankInfo
    {
        public string BankName { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
        public string AccountName { get; set; } = string.Empty;
        public string TransferContent { get; set; } = string.Empty;
    }

    public class EWalletInfo
    {
        public string WalletType { get; set; } = string.Empty;
        public string? QRCodeUrl { get; set; }
        public string? DeepLink { get; set; }
    }

    public class CheckoutViewModel
    {
        public Order Order { get; set; } = new Order();
        public List<CartItem> CartItems { get; set; } = new List<CartItem>();
        public decimal CartTotal { get; set; }
        public decimal ShippingFee { get; set; } = 0;
        public decimal DiscountAmount { get; set; } = 0;
        public decimal FinalTotal { get; set; }

        // Payment details
        public PaymentRequest PaymentRequest { get; set; } = new PaymentRequest();
        
        // Coupon
        public string? CouponCode { get; set; }
        public Coupon? AppliedCoupon { get; set; }

        // User info (if logged in)
        public ApplicationUser? User { get; set; }
    }

    public class PaymentViewModel
    {
        public Order Order { get; set; } = new Order();
        public PaymentResult? PaymentResult { get; set; }
        public PaymentRequest PaymentRequest { get; set; } = new PaymentRequest();
    }

    public class PaymentStatusViewModel
    {
        public string OrderNumber { get; set; } = string.Empty;
        public string? TransactionId { get; set; }
        public PaymentStatus Status { get; set; }
        public PaymentMethod PaymentMethod { get; set; }
        public decimal Amount { get; set; }
        public DateTime ProcessedAt { get; set; }
        public string? Message { get; set; }
        public BankInfo? BankInfo { get; set; }
        public EWalletInfo? EWalletInfo { get; set; }
    }
}
