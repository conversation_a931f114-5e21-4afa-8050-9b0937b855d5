{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "CoffeeShopWebsite.styles.css", "AssetFile": "CoffeeShopWebsite.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001828153565"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "546"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pwZUpmRnxKZQR/9q/eseStD/E1DFbvQbGS5z3ci3M/w=\""}, {"Name": "ETag", "Value": "W/\"j7ePhHo/d2livFrc8clIEPJimXirQ1mNnltYZMKXRWU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j7ePhHo/d2livFrc8clIEPJimXirQ1mNnltYZMKXRWU="}]}, {"Route": "CoffeeShopWebsite.styles.css", "AssetFile": "CoffeeShopWebsite.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j7ePhHo/d2livFrc8clIEPJimXirQ1mNnltYZMKXRWU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j7ePhHo/d2livFrc8clIEPJimXirQ1mNnltYZMKXRWU="}]}, {"Route": "CoffeeShopWebsite.styles.css.gz", "AssetFile": "CoffeeShopWebsite.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "546"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pwZUpmRnxKZQR/9q/eseStD/E1DFbvQbGS5z3ci3M/w=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pwZUpmRnxKZQR/9q/eseStD/E1DFbvQbGS5z3ci3M/w="}]}, {"Route": "CoffeeShopWebsite.vo10bh2rmr.styles.css", "AssetFile": "CoffeeShopWebsite.styles.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001828153565"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "546"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pwZUpmRnxKZQR/9q/eseStD/E1DFbvQbGS5z3ci3M/w=\""}, {"Name": "ETag", "Value": "W/\"j7ePhHo/d2livFrc8clIEPJimXirQ1mNnltYZMKXRWU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vo10bh2rmr"}, {"Name": "integrity", "Value": "sha256-j7ePhHo/d2livFrc8clIEPJimXirQ1mNnltYZMKXRWU="}, {"Name": "label", "Value": "CoffeeShopWebsite.styles.css"}]}, {"Route": "CoffeeShopWebsite.vo10bh2rmr.styles.css", "AssetFile": "CoffeeShopWebsite.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j7ePhHo/d2livFrc8clIEPJimXirQ1mNnltYZMKXRWU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vo10bh2rmr"}, {"Name": "integrity", "Value": "sha256-j7ePhHo/d2livFrc8clIEPJimXirQ1mNnltYZMKXRWU="}, {"Name": "label", "Value": "CoffeeShopWebsite.styles.css"}]}, {"Route": "CoffeeShopWebsite.vo10bh2rmr.styles.css.gz", "AssetFile": "CoffeeShopWebsite.styles.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "546"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"pwZUpmRnxKZQR/9q/eseStD/E1DFbvQbGS5z3ci3M/w=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vo10bh2rmr"}, {"Name": "integrity", "Value": "sha256-pwZUpmRnxKZQR/9q/eseStD/E1DFbvQbGS5z3ci3M/w="}, {"Name": "label", "Value": "CoffeeShopWebsite.styles.css.gz"}]}, {"Route": "css/site.1wtdaw6cmf.css", "AssetFile": "css/site.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000548546352"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1822"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NOlw/l5/6u5spMuWF63SX/KLleOHut0lN4u3XKd73Zk=\""}, {"Name": "ETag", "Value": "W/\"kQJmz7Wd+lAO7doBUFhmv+v9HV15lE6ev1ptMfDuaWs=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 18:52:50 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1wtdaw6cmf"}, {"Name": "integrity", "Value": "sha256-kQJmz7Wd+lAO7doBUFhmv+v9HV15lE6ev1ptMfDuaWs="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.1wtdaw6cmf.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7175"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kQJmz7Wd+lAO7doBUFhmv+v9HV15lE6ev1ptMfDuaWs=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 18:52:40 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1wtdaw6cmf"}, {"Name": "integrity", "Value": "sha256-kQJmz7Wd+lAO7doBUFhmv+v9HV15lE6ev1ptMfDuaWs="}, {"Name": "label", "Value": "css/site.css"}]}, {"Route": "css/site.1wtdaw6cmf.css.gz", "AssetFile": "css/site.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1822"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NOlw/l5/6u5spMuWF63SX/KLleOHut0lN4u3XKd73Zk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 18:52:50 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1wtdaw6cmf"}, {"Name": "integrity", "Value": "sha256-NOlw/l5/6u5spMuWF63SX/KLleOHut0lN4u3XKd73Zk="}, {"Name": "label", "Value": "css/site.css.gz"}]}, {"Route": "css/site.css", "AssetFile": "css/site.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000548546352"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1822"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NOlw/l5/6u5spMuWF63SX/KLleOHut0lN4u3XKd73Zk=\""}, {"Name": "ETag", "Value": "W/\"kQJmz7Wd+lAO7doBUFhmv+v9HV15lE6ev1ptMfDuaWs=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 18:52:50 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kQJmz7Wd+lAO7doBUFhmv+v9HV15lE6ev1ptMfDuaWs="}]}, {"Route": "css/site.css", "AssetFile": "css/site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7175"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kQJmz7Wd+lAO7doBUFhmv+v9HV15lE6ev1ptMfDuaWs=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 18:52:40 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kQJmz7Wd+lAO7doBUFhmv+v9HV15lE6ev1ptMfDuaWs="}]}, {"Route": "css/site.css.gz", "AssetFile": "css/site.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1822"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NOlw/l5/6u5spMuWF63SX/KLleOHut0lN4u3XKd73Zk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 18:52:50 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NOlw/l5/6u5spMuWF63SX/KLleOHut0lN4u3XKd73Zk="}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "favicon.ico.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000405022276"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=\""}, {"Name": "ETag", "Value": "W/\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.61n19gt1b8.ico.gz", "AssetFile": "favicon.ico.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "integrity", "Value": "sha256-wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0="}, {"Name": "label", "Value": "favicon.ico.gz"}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000405022276"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=\""}, {"Name": "ETag", "Value": "W/\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.ico.gz", "AssetFile": "favicon.ico.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2468"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0="}]}, {"Route": "images/products/placeholder.html", "AssetFile": "images/products/placeholder.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001400560224"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "713"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"RyeCtxK7CWpRMq295U2WRPeokLRXQ61SlDFi6uQwjvc=\""}, {"Name": "ETag", "Value": "W/\"e2ZbC9X/oWkFqhSR0Mq7eXGD5u3G2Qh+qzYvR75UqYs=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:34:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-e2ZbC9X/oWkFqhSR0Mq7eXGD5u3G2Qh+qzYvR75UqYs="}]}, {"Route": "images/products/placeholder.html", "AssetFile": "images/products/placeholder.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1849"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"e2ZbC9X/oWkFqhSR0Mq7eXGD5u3G2Qh+qzYvR75UqYs=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:33:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-e2ZbC9X/oWkFqhSR0Mq7eXGD5u3G2Qh+qzYvR75UqYs="}]}, {"Route": "images/products/placeholder.html.gz", "AssetFile": "images/products/placeholder.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "713"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"RyeCtxK7CWpRMq295U2WRPeokLRXQ61SlDFi6uQwjvc=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:34:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RyeCtxK7CWpRMq295U2WRPeokLRXQ61SlDFi6uQwjvc="}]}, {"Route": "images/products/placeholder.v7q7uigigt.html", "AssetFile": "images/products/placeholder.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001400560224"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "713"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"RyeCtxK7CWpRMq295U2WRPeokLRXQ61SlDFi6uQwjvc=\""}, {"Name": "ETag", "Value": "W/\"e2ZbC9X/oWkFqhSR0Mq7eXGD5u3G2Qh+qzYvR75UqYs=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:34:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v7q7uigigt"}, {"Name": "integrity", "Value": "sha256-e2ZbC9X/oWkFqhSR0Mq7eXGD5u3G2Qh+qzYvR75UqYs="}, {"Name": "label", "Value": "images/products/placeholder.html"}]}, {"Route": "images/products/placeholder.v7q7uigigt.html", "AssetFile": "images/products/placeholder.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1849"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"e2ZbC9X/oWkFqhSR0Mq7eXGD5u3G2Qh+qzYvR75UqYs=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:33:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v7q7uigigt"}, {"Name": "integrity", "Value": "sha256-e2ZbC9X/oWkFqhSR0Mq7eXGD5u3G2Qh+qzYvR75UqYs="}, {"Name": "label", "Value": "images/products/placeholder.html"}]}, {"Route": "images/products/placeholder.v7q7uigigt.html.gz", "AssetFile": "images/products/placeholder.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "713"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"RyeCtxK7CWpRMq295U2WRPeokLRXQ61SlDFi6uQwjvc=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:34:43 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v7q7uigigt"}, {"Name": "integrity", "Value": "sha256-RyeCtxK7CWpRMq295U2WRPeokLRXQ61SlDFi6uQwjvc="}, {"Name": "label", "Value": "images/products/placeholder.html.gz"}]}, {"Route": "js/site.js", "AssetFile": "js/site.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.005263157895"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "189"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=\""}, {"Name": "ETag", "Value": "W/\"hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo="}]}, {"Route": "js/site.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "231"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo="}]}, {"Route": "js/site.js.gz", "AssetFile": "js/site.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "189"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA="}]}, {"Route": "js/site.xtxxf3hu2r.js", "AssetFile": "js/site.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.005263157895"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "189"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=\""}, {"Name": "ETag", "Value": "W/\"hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xtxxf3hu2r"}, {"Name": "integrity", "Value": "sha256-hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo="}, {"Name": "label", "Value": "js/site.js"}]}, {"Route": "js/site.xtxxf3hu2r.js", "AssetFile": "js/site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "231"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xtxxf3hu2r"}, {"Name": "integrity", "Value": "sha256-hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo="}, {"Name": "label", "Value": "js/site.js"}]}, {"Route": "js/site.xtxxf3hu2r.js.gz", "AssetFile": "js/site.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "189"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xtxxf3hu2r"}, {"Name": "integrity", "Value": "sha256-Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA="}, {"Name": "label", "Value": "js/site.js.gz"}]}, {"Route": "lib/bootstrap/LICENSE", "AssetFile": "lib/bootstrap/LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}]}, {"Route": "lib/bootstrap/LICENSE.81b7ukuj9c", "AssetFile": "lib/bootstrap/LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "81b7ukuj9c"}, {"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}, {"Name": "label", "Value": "lib/bootstrap/LICENSE"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.bqjiyaj88i.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148235992"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6745"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=\""}, {"Name": "ETag", "Value": "W/\"Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bqjiyaj88i"}, {"Name": "integrity", "Value": "sha256-Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.bqjiyaj88i.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "70329"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bqjiyaj88i"}, {"Name": "integrity", "Value": "sha256-Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.bqjiyaj88i.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6745"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bqjiyaj88i"}, {"Name": "integrity", "Value": "sha256-jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148235992"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6745"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=\""}, {"Name": "ETag", "Value": "W/\"Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "70329"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030492453"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=\""}, {"Name": "ETag", "Value": "W/\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "203221"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6745"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030492453"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=\""}, {"Name": "ETag", "Value": "W/\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "203221"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32794"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167504188"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5969"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=\""}, {"Name": "ETag", "Value": "W/\"5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51795"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072421784"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=\""}, {"Name": "ETag", "Value": "W/\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "115986"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "integrity", "Value": "sha256-oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5969"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072421784"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=\""}, {"Name": "ETag", "Value": "W/\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "115986"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.erw9l3u2r3.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167504188"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5969"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=\""}, {"Name": "ETag", "Value": "W/\"5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "erw9l3u2r3"}, {"Name": "integrity", "Value": "sha256-5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.erw9l3u2r3.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51795"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "erw9l3u2r3"}, {"Name": "integrity", "Value": "sha256-5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.erw9l3u2r3.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5969"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "erw9l3u2r3"}, {"Name": "integrity", "Value": "sha256-y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148148148"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6749"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=\""}, {"Name": "ETag", "Value": "W/\"CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "70403"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030493383"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=\""}, {"Name": "ETag", "Value": "W/\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "203225"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "integrity", "Value": "sha256-cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6749"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030493383"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=\""}, {"Name": "ETag", "Value": "W/\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "203225"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "32793"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.d7shbmvgxk.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000148148148"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6749"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=\""}, {"Name": "ETag", "Value": "W/\"CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d7shbmvgxk"}, {"Name": "integrity", "Value": "sha256-CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.d7shbmvgxk.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "70403"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d7shbmvgxk"}, {"Name": "integrity", "Value": "sha256-CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.d7shbmvgxk.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6749"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d7shbmvgxk"}, {"Name": "integrity", "Value": "sha256-P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167448091"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5971"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=\""}, {"Name": "ETag", "Value": "W/\"vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51870"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072379849"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=\""}, {"Name": "ETag", "Value": "W/\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "116063"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "integrity", "Value": "sha256-V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5971"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000072379849"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=\""}, {"Name": "ETag", "Value": "W/\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "116063"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13815"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.k8d9w2qqmf.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000167448091"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5971"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=\""}, {"Name": "ETag", "Value": "W/\"vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k8d9w2qqmf"}, {"Name": "integrity", "Value": "sha256-vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.k8d9w2qqmf.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51870"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k8d9w2qqmf"}, {"Name": "integrity", "Value": "sha256-vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.k8d9w2qqmf.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5971"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k8d9w2qqmf"}, {"Name": "integrity", "Value": "sha256-ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000295770482"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3380"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=\""}, {"Name": "ETag", "Value": "W/\"lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12065"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038726667"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=\""}, {"Name": "ETag", "Value": "W/\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "129371"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "integrity", "Value": "sha256-FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3380"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038726667"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=\""}, {"Name": "ETag", "Value": "W/\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "129371"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25821"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.b7pk76d08c.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000311138768"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3213"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=\""}, {"Name": "ETag", "Value": "W/\"l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b7pk76d08c"}, {"Name": "integrity", "Value": "sha256-l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.b7pk76d08c.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10126"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b7pk76d08c"}, {"Name": "integrity", "Value": "sha256-l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.b7pk76d08c.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3213"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b7pk76d08c"}, {"Name": "integrity", "Value": "sha256-Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000311138768"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3213"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=\""}, {"Name": "ETag", "Value": "W/\"l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10126"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000079440737"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=\""}, {"Name": "ETag", "Value": "W/\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "51369"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "integrity", "Value": "sha256-crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3213"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000079440737"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=\""}, {"Name": "ETag", "Value": "W/\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "51369"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "12587"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000296912114"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3367"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=\""}, {"Name": "ETag", "Value": "W/\"V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "12058"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038708678"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=\""}, {"Name": "ETag", "Value": "W/\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "129386"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "integrity", "Value": "sha256-wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3367"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000038708678"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=\""}, {"Name": "ETag", "Value": "W/\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "129386"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "25833"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000307976594"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3246"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=\""}, {"Name": "ETag", "Value": "W/\"/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10198"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3246"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000066423115"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=\""}, {"Name": "ETag", "Value": "W/\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "63943"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "integrity", "Value": "sha256-V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000066423115"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=\""}, {"Name": "ETag", "Value": "W/\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "63943"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15054"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.dxx9fxp4il.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000307976594"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3246"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=\""}, {"Name": "ETag", "Value": "W/\"/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dxx9fxp4il"}, {"Name": "integrity", "Value": "sha256-/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.dxx9fxp4il.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10198"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dxx9fxp4il"}, {"Name": "integrity", "Value": "sha256-/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.dxx9fxp4il.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3246"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dxx9fxp4il"}, {"Name": "integrity", "Value": "sha256-qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rzd6atqjts.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000296912114"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3367"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=\""}, {"Name": "ETag", "Value": "W/\"V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rzd6atqjts"}, {"Name": "integrity", "Value": "sha256-V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rzd6atqjts.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12058"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rzd6atqjts"}, {"Name": "integrity", "Value": "sha256-V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rzd6atqjts.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3367"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rzd6atqjts"}, {"Name": "integrity", "Value": "sha256-bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.ub07r2b239.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000295770482"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3380"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=\""}, {"Name": "ETag", "Value": "W/\"lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ub07r2b239"}, {"Name": "integrity", "Value": "sha256-lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.ub07r2b239.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "12065"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ub07r2b239"}, {"Name": "integrity", "Value": "sha256-lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.ub07r2b239.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3380"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ub07r2b239"}, {"Name": "integrity", "Value": "sha256-+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083388926"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11991"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=\""}, {"Name": "ETag", "Value": "W/\"2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "107823"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11991"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022663403"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=\""}, {"Name": "ETag", "Value": "W/\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "267535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022663403"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=\""}, {"Name": "ETag", "Value": "W/\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "267535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "integrity", "Value": "sha256-kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.khv3u5hwcm.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083388926"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11991"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=\""}, {"Name": "ETag", "Value": "W/\"2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3u5hwcm"}, {"Name": "integrity", "Value": "sha256-2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.khv3u5hwcm.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "107823"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3u5hwcm"}, {"Name": "integrity", "Value": "sha256-2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.khv3u5hwcm.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11991"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3u5hwcm"}, {"Name": "integrity", "Value": "sha256-8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090383225"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11063"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=\""}, {"Name": "ETag", "Value": "W/\"KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "85352"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041081259"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=\""}, {"Name": "ETag", "Value": "W/\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "integrity", "Value": "sha256-Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11063"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041081259"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=\""}, {"Name": "ETag", "Value": "W/\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24341"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.lcd1t2u6c8.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090383225"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11063"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=\""}, {"Name": "ETag", "Value": "W/\"KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lcd1t2u6c8"}, {"Name": "integrity", "Value": "sha256-KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.lcd1t2u6c8.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "85352"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lcd1t2u6c8"}, {"Name": "integrity", "Value": "sha256-KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.lcd1t2u6c8.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11063"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lcd1t2u6c8"}, {"Name": "integrity", "Value": "sha256-xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083794201"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11933"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=\""}, {"Name": "ETag", "Value": "W/\"H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "107691"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11933"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022677794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=\""}, {"Name": "ETag", "Value": "W/\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "267476"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "integrity", "Value": "sha256-KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022677794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=\""}, {"Name": "ETag", "Value": "W/\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "267476"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44095"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.06098lyss8.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090522314"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=\""}, {"Name": "ETag", "Value": "W/\"GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "06098lyss8"}, {"Name": "integrity", "Value": "sha256-GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.06098lyss8.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "85281"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "06098lyss8"}, {"Name": "integrity", "Value": "sha256-GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.06098lyss8.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "06098lyss8"}, {"Name": "integrity", "Value": "sha256-hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000090522314"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=\""}, {"Name": "ETag", "Value": "W/\"GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "85281"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041162427"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=\""}, {"Name": "ETag", "Value": "W/\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041162427"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=\""}, {"Name": "ETag", "Value": "W/\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24293"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "integrity", "Value": "sha256-8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.tdbxkamptv.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083794201"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11933"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=\""}, {"Name": "ETag", "Value": "W/\"H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tdbxkamptv"}, {"Name": "integrity", "Value": "sha256-H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.tdbxkamptv.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "107691"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tdbxkamptv"}, {"Name": "integrity", "Value": "sha256-H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.tdbxkamptv.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11933"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tdbxkamptv"}, {"Name": "integrity", "Value": "sha256-QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030073379"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33251"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=\""}, {"Name": "ETag", "Value": "W/\"GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "281046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33251"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008694896"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=\""}, {"Name": "ETag", "Value": "W/\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "679755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008694896"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=\""}, {"Name": "ETag", "Value": "W/\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "679755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "integrity", "Value": "sha256-KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.css.pj5nd1wqec.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "115009"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pj5nd1wqec"}, {"Name": "integrity", "Value": "sha256-M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.46ein0sx1k.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032295569"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30963"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=\""}, {"Name": "ETag", "Value": "W/\"PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "46ein0sx1k"}, {"Name": "integrity", "Value": "sha256-PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.46ein0sx1k.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "232803"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "46ein0sx1k"}, {"Name": "integrity", "Value": "sha256-PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.46ein0sx1k.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30963"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "46ein0sx1k"}, {"Name": "integrity", "Value": "sha256-NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032295569"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30963"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=\""}, {"Name": "ETag", "Value": "W/\"PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "232803"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30963"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010892297"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=\""}, {"Name": "ETag", "Value": "W/\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "589892"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010892297"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=\""}, {"Name": "ETag", "Value": "W/\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "589892"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "integrity", "Value": "sha256-8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.min.css.v0zj4ognzu.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91807"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v0zj4ognzu"}, {"Name": "integrity", "Value": "sha256-+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.37tfw0ft22.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030209655"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33101"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=\""}, {"Name": "ETag", "Value": "W/\"j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "37tfw0ft22"}, {"Name": "integrity", "Value": "sha256-j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.37tfw0ft22.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "280259"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "37tfw0ft22"}, {"Name": "integrity", "Value": "sha256-j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.37tfw0ft22.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33101"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "37tfw0ft22"}, {"Name": "integrity", "Value": "sha256-Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030209655"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33101"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=\""}, {"Name": "ETag", "Value": "W/\"j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "280259"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33101"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008699132"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=\""}, {"Name": "ETag", "Value": "W/\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "679615"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "integrity", "Value": "sha256-xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000008699132"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=\""}, {"Name": "ETag", "Value": "W/\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "679615"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "114953"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032271598"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30986"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=\""}, {"Name": "ETag", "Value": "W/\"h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "232911"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010904769"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=\""}, {"Name": "ETag", "Value": "W/\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "589087"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "integrity", "Value": "sha256-mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30986"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010904769"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=\""}, {"Name": "ETag", "Value": "W/\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "589087"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "91702"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.pk9g2wxc8p.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032271598"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30986"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=\""}, {"Name": "ETag", "Value": "W/\"h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pk9g2wxc8p"}, {"Name": "integrity", "Value": "sha256-h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.pk9g2wxc8p.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "232911"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pk9g2wxc8p"}, {"Name": "integrity", "Value": "sha256-h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.pk9g2wxc8p.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30986"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pk9g2wxc8p"}, {"Name": "integrity", "Value": "sha256-Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.s35ty4nyc5.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000030073379"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33251"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=\""}, {"Name": "ETag", "Value": "W/\"GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s35ty4nyc5"}, {"Name": "integrity", "Value": "sha256-GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.s35ty4nyc5.css", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "281046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s35ty4nyc5"}, {"Name": "integrity", "Value": "sha256-GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css"}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.s35ty4nyc5.css.gz", "AssetFile": "lib/bootstrap/dist/css/bootstrap.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "33251"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s35ty4nyc5"}, {"Name": "integrity", "Value": "sha256-I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU="}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.css.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.6cfz1n2cew.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022545373"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44354"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=\""}, {"Name": "ETag", "Value": "W/\"mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6cfz1n2cew"}, {"Name": "integrity", "Value": "sha256-mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.6cfz1n2cew.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "207819"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6cfz1n2cew"}, {"Name": "integrity", "Value": "sha256-mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.6cfz1n2cew.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44354"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6cfz1n2cew"}, {"Name": "integrity", "Value": "sha256-8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000022545373"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44354"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=\""}, {"Name": "ETag", "Value": "W/\"mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "207819"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.6pdc2jztkx.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010864133"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "92045"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=\""}, {"Name": "ETag", "Value": "W/\"Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pdc2jztkx"}, {"Name": "integrity", "Value": "sha256-Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.6pdc2jztkx.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "444579"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pdc2jztkx"}, {"Name": "integrity", "Value": "sha256-Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.6pdc2jztkx.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "92045"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pdc2jztkx"}, {"Name": "integrity", "Value": "sha256-tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "44354"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000010864133"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "92045"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=\""}, {"Name": "ETag", "Value": "W/\"Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "444579"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "92045"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.493y06b0oq.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041692725"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23984"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=\""}, {"Name": "ETag", "Value": "W/\"CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "493y06b0oq"}, {"Name": "integrity", "Value": "sha256-CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.493y06b0oq.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "80721"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "493y06b0oq"}, {"Name": "integrity", "Value": "sha256-CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.493y06b0oq.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23984"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "493y06b0oq"}, {"Name": "integrity", "Value": "sha256-PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041692725"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23984"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=\""}, {"Name": "ETag", "Value": "W/\"CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "80721"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "23984"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.iovd86k7lj.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011499937"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86956"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=\""}, {"Name": "ETag", "Value": "W/\"Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iovd86k7lj"}, {"Name": "integrity", "Value": "sha256-Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.iovd86k7lj.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "332090"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iovd86k7lj"}, {"Name": "integrity", "Value": "sha256-Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.iovd86k7lj.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86956"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iovd86k7lj"}, {"Name": "integrity", "Value": "sha256-WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011499937"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86956"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=\""}, {"Name": "ETag", "Value": "W/\"Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "332090"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "86956"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000034658441"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "28852"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=\""}, {"Name": "ETag", "Value": "W/\"exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "135829"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "28852"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.kbrnm935zg.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015593083"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64130"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=\""}, {"Name": "ETag", "Value": "W/\"EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kbrnm935zg"}, {"Name": "integrity", "Value": "sha256-EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.kbrnm935zg.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "305438"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kbrnm935zg"}, {"Name": "integrity", "Value": "sha256-EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.kbrnm935zg.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64130"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kbrnm935zg"}, {"Name": "integrity", "Value": "sha256-UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015593083"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64130"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=\""}, {"Name": "ETag", "Value": "W/\"EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "305438"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64130"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.jj8uyg4cgr.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000053659584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=\""}, {"Name": "ETag", "Value": "W/\"QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jj8uyg4cgr"}, {"Name": "integrity", "Value": "sha256-QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.jj8uyg4cgr.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "73935"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jj8uyg4cgr"}, {"Name": "integrity", "Value": "sha256-QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.jj8uyg4cgr.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jj8uyg4cgr"}, {"Name": "integrity", "Value": "sha256-WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000053659584"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=\""}, {"Name": "ETag", "Value": "W/\"QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "73935"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "18635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017646644"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "56667"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=\""}, {"Name": "ETag", "Value": "W/\"Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "222455"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "56667"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.y7v9cxd14o.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017646644"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "56667"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=\""}, {"Name": "ETag", "Value": "W/\"Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y7v9cxd14o"}, {"Name": "integrity", "Value": "sha256-Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.y7v9cxd14o.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "222455"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y7v9cxd14o"}, {"Name": "integrity", "Value": "sha256-Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.y7v9cxd14o.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "56667"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y7v9cxd14o"}, {"Name": "integrity", "Value": "sha256-OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.vr1egmr9el.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000034658441"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "28852"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=\""}, {"Name": "ETag", "Value": "W/\"exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vr1egmr9el"}, {"Name": "integrity", "Value": "sha256-exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.vr1egmr9el.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "135829"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vr1egmr9el"}, {"Name": "integrity", "Value": "sha256-exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.vr1egmr9el.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "28852"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vr1egmr9el"}, {"Name": "integrity", "Value": "sha256-6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000033818059"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "29569"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=\""}, {"Name": "ETag", "Value": "W/\"+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "145401"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "29569"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.h1s4sie4z3.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015522166"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64423"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=\""}, {"Name": "ETag", "Value": "W/\"9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h1s4sie4z3"}, {"Name": "integrity", "Value": "sha256-9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.h1s4sie4z3.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "306606"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h1s4sie4z3"}, {"Name": "integrity", "Value": "sha256-9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.h1s4sie4z3.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64423"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h1s4sie4z3"}, {"Name": "integrity", "Value": "sha256-rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015522166"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64423"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=\""}, {"Name": "ETag", "Value": "W/\"9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "306606"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64423"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.63fj8s7r0e.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000060106990"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16636"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=\""}, {"Name": "ETag", "Value": "W/\"3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "63fj8s7r0e"}, {"Name": "integrity", "Value": "sha256-3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.63fj8s7r0e.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "60635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "63fj8s7r0e"}, {"Name": "integrity", "Value": "sha256-3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.63fj8s7r0e.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16636"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "63fj8s7r0e"}, {"Name": "integrity", "Value": "sha256-bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000060106990"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16636"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=\""}, {"Name": "ETag", "Value": "W/\"3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "60635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.0j3bgjxly4.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017905424"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55848"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=\""}, {"Name": "ETag", "Value": "W/\"ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0j3bgjxly4"}, {"Name": "integrity", "Value": "sha256-ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.0j3bgjxly4.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "220561"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0j3bgjxly4"}, {"Name": "integrity", "Value": "sha256-ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.0j3bgjxly4.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55848"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0j3bgjxly4"}, {"Name": "integrity", "Value": "sha256-OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "16636"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000017905424"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55848"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=\""}, {"Name": "ETag", "Value": "W/\"ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "220561"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "55848"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.notf2xhcfb.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000033818059"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "29569"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=\""}, {"Name": "ETag", "Value": "W/\"+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "notf2xhcfb"}, {"Name": "integrity", "Value": "sha256-+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.notf2xhcfb.js", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "145401"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "notf2xhcfb"}, {"Name": "integrity", "Value": "sha256-+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.notf2xhcfb.js.gz", "AssetFile": "lib/bootstrap/dist/js/bootstrap.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "29569"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "notf2xhcfb"}, {"Name": "integrity", "Value": "sha256-6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ="}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.gz"}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.356vix0kms.txt", "AssetFile": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001438848921"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "694"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=\""}, {"Name": "ETag", "Value": "W/\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "356vix0kms"}, {"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt"}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.356vix0kms.txt", "AssetFile": "lib/jquery-validation-unobtrusive/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "356vix0kms"}, {"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt"}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.356vix0kms.txt.gz", "AssetFile": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "694"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "356vix0kms"}, {"Name": "integrity", "Value": "sha256-WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz"}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001438848921"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "694"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=\""}, {"Name": "ETag", "Value": "W/\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "lib/jquery-validation-unobtrusive/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetFile": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "694"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.47otxtyo56.js", "AssetFile": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000214961307"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=\""}, {"Name": "ETag", "Value": "W/\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47otxtyo56"}, {"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js"}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.47otxtyo56.js", "AssetFile": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47otxtyo56"}, {"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js"}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.47otxtyo56.js.gz", "AssetFile": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47otxtyo56"}, {"Name": "integrity", "Value": "sha256-8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz"}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "AssetFile": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000214961307"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=\""}, {"Name": "ETag", "Value": "W/\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "AssetFile": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetFile": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.4v8eqarkd7.js", "AssetFile": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000452898551"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2207"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=\""}, {"Name": "ETag", "Value": "W/\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v8eqarkd7"}, {"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js"}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.4v8eqarkd7.js", "AssetFile": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v8eqarkd7"}, {"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js"}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.4v8eqarkd7.js.gz", "AssetFile": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2207"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v8eqarkd7"}, {"Name": "integrity", "Value": "sha256-ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus="}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz"}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "AssetFile": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000452898551"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2207"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=\""}, {"Name": "ETag", "Value": "W/\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "AssetFile": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetFile": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2207"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus="}]}, {"Route": "lib/jquery-validation/LICENSE.md", "AssetFile": "lib/jquery-validation/LICENSE.md.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001461988304"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "683"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=\""}, {"Name": "ETag", "Value": "W/\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.md", "AssetFile": "lib/jquery-validation/LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.md.gz", "AssetFile": "lib/jquery-validation/LICENSE.md.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "683"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4="}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md", "AssetFile": "lib/jquery-validation/LICENSE.md.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001461988304"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "683"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=\""}, {"Name": "ETag", "Value": "W/\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md"}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md", "AssetFile": "lib/jquery-validation/LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md"}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md.gz", "AssetFile": "lib/jquery-validation/LICENSE.md.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "683"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "integrity", "Value": "sha256-7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4="}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md.gz"}]}, {"Route": "lib/jquery-validation/dist/additional-methods.83jwlth58m.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071027772"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14078"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=\""}, {"Name": "ETag", "Value": "W/\"XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "83jwlth58m"}, {"Name": "integrity", "Value": "sha256-XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0="}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js"}]}, {"Route": "lib/jquery-validation/dist/additional-methods.83jwlth58m.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "53033"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "83jwlth58m"}, {"Name": "integrity", "Value": "sha256-XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0="}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js"}]}, {"Route": "lib/jquery-validation/dist/additional-methods.83jwlth58m.js.gz", "AssetFile": "lib/jquery-validation/dist/additional-methods.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14078"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "83jwlth58m"}, {"Name": "integrity", "Value": "sha256-BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM="}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js.gz"}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071027772"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14078"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=\""}, {"Name": "ETag", "Value": "W/\"XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "53033"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetFile": "lib/jquery-validation/dist/additional-methods.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14078"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000154249576"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6482"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=\""}, {"Name": "ETag", "Value": "W/\"jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "22125"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetFile": "lib/jquery-validation/dist/additional-methods.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6482"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.mrlpezrjn3.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000154249576"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6482"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=\""}, {"Name": "ETag", "Value": "W/\"jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mrlpezrjn3"}, {"Name": "integrity", "Value": "sha256-jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8="}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js"}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.mrlpezrjn3.js", "AssetFile": "lib/jquery-validation/dist/additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "22125"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mrlpezrjn3"}, {"Name": "integrity", "Value": "sha256-jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8="}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js"}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.mrlpezrjn3.js.gz", "AssetFile": "lib/jquery-validation/dist/additional-methods.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6482"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mrlpezrjn3"}, {"Name": "integrity", "Value": "sha256-hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg="}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js.gz"}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071078257"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14068"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=\""}, {"Name": "ETag", "Value": "W/\"kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "52536"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetFile": "lib/jquery-validation/dist/jquery.validate.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14068"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.lzl9nlhx6b.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000071078257"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14068"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=\""}, {"Name": "ETag", "Value": "W/\"kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lzl9nlhx6b"}, {"Name": "integrity", "Value": "sha256-kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg="}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js"}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.lzl9nlhx6b.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "52536"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lzl9nlhx6b"}, {"Name": "integrity", "Value": "sha256-kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg="}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js"}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.lzl9nlhx6b.js.gz", "AssetFile": "lib/jquery-validation/dist/jquery.validate.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "14068"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lzl9nlhx6b"}, {"Name": "integrity", "Value": "sha256-KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8="}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js.gz"}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.ag7o75518u.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000123122384"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8121"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=\""}, {"Name": "ETag", "Value": "W/\"umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ag7o75518u"}, {"Name": "integrity", "Value": "sha256-umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4="}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js"}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.ag7o75518u.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "25308"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ag7o75518u"}, {"Name": "integrity", "Value": "sha256-umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4="}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js"}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.ag7o75518u.js.gz", "AssetFile": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8121"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ag7o75518u"}, {"Name": "integrity", "Value": "sha256-T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0="}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js.gz"}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000123122384"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8121"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=\""}, {"Name": "ETag", "Value": "W/\"umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "lib/jquery-validation/dist/jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "25308"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetFile": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8121"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0="}]}, {"Route": "lib/jquery/LICENSE.mlv21k5csn.txt", "AssetFile": "lib/jquery/LICENSE.txt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001464128843"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "682"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=\""}, {"Name": "ETag", "Value": "W/\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mlv21k5csn"}, {"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt"}]}, {"Route": "lib/jquery/LICENSE.mlv21k5csn.txt", "AssetFile": "lib/jquery/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mlv21k5csn"}, {"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt"}]}, {"Route": "lib/jquery/LICENSE.mlv21k5csn.txt.gz", "AssetFile": "lib/jquery/LICENSE.txt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "682"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mlv21k5csn"}, {"Name": "integrity", "Value": "sha256-JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y="}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt.gz"}]}, {"Route": "lib/jquery/LICENSE.txt", "AssetFile": "lib/jquery/LICENSE.txt.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001464128843"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "682"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=\""}, {"Name": "ETag", "Value": "W/\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "lib/jquery/LICENSE.txt", "AssetFile": "lib/jquery/LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "lib/jquery/LICENSE.txt.gz", "AssetFile": "lib/jquery/LICENSE.txt.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "682"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y="}]}, {"Route": "lib/jquery/dist/jquery.0i3buxo5is.js", "AssetFile": "lib/jquery/dist/jquery.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011843851"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "84431"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=\""}, {"Name": "ETag", "Value": "W/\"eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0i3buxo5is"}, {"Name": "integrity", "Value": "sha256-e<PERSON>hayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js"}]}, {"Route": "lib/jquery/dist/jquery.0i3buxo5is.js", "AssetFile": "lib/jquery/dist/jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "285314"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0i3buxo5is"}, {"Name": "integrity", "Value": "sha256-e<PERSON>hayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js"}]}, {"Route": "lib/jquery/dist/jquery.0i3buxo5is.js.gz", "AssetFile": "lib/jquery/dist/jquery.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "84431"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0i3buxo5is"}, {"Name": "integrity", "Value": "sha256-YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js.gz"}]}, {"Route": "lib/jquery/dist/jquery.js", "AssetFile": "lib/jquery/dist/jquery.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000011843851"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "84431"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=\""}, {"Name": "ETag", "Value": "W/\"eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-e<PERSON>hayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4="}]}, {"Route": "lib/jquery/dist/jquery.js", "AssetFile": "lib/jquery/dist/jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "285314"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-e<PERSON>hayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4="}]}, {"Route": "lib/jquery/dist/jquery.js.gz", "AssetFile": "lib/jquery/dist/jquery.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "84431"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU="}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "lib/jquery/dist/jquery.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032590275"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30683"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=\""}, {"Name": "ETag", "Value": "W/\"/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "87533"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="}]}, {"Route": "lib/jquery/dist/jquery.min.js.gz", "AssetFile": "lib/jquery/dist/jquery.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30683"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk="}]}, {"Route": "lib/jquery/dist/jquery.min.map", "AssetFile": "lib/jquery/dist/jquery.min.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018363112"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "54456"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=\""}, {"Name": "ETag", "Value": "W/\"z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg="}]}, {"Route": "lib/jquery/dist/jquery.min.map", "AssetFile": "lib/jquery/dist/jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "134755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg="}]}, {"Route": "lib/jquery/dist/jquery.min.map.gz", "AssetFile": "lib/jquery/dist/jquery.min.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "54456"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag="}]}, {"Route": "lib/jquery/dist/jquery.min.o1o13a6vjx.js", "AssetFile": "lib/jquery/dist/jquery.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000032590275"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30683"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=\""}, {"Name": "ETag", "Value": "W/\"/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o1o13a6vjx"}, {"Name": "integrity", "Value": "sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}]}, {"Route": "lib/jquery/dist/jquery.min.o1o13a6vjx.js", "AssetFile": "lib/jquery/dist/jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "87533"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o1o13a6vjx"}, {"Name": "integrity", "Value": "sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}]}, {"Route": "lib/jquery/dist/jquery.min.o1o13a6vjx.js.gz", "AssetFile": "lib/jquery/dist/jquery.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "30683"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o1o13a6vjx"}, {"Name": "integrity", "Value": "sha256-rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js.gz"}]}, {"Route": "lib/jquery/dist/jquery.min.ttgo8qnofa.map", "AssetFile": "lib/jquery/dist/jquery.min.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000018363112"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "54456"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=\""}, {"Name": "ETag", "Value": "W/\"z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ttgo8qnofa"}, {"Name": "integrity", "Value": "sha256-z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map"}]}, {"Route": "lib/jquery/dist/jquery.min.ttgo8qnofa.map", "AssetFile": "lib/jquery/dist/jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "134755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ttgo8qnofa"}, {"Name": "integrity", "Value": "sha256-z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map"}]}, {"Route": "lib/jquery/dist/jquery.min.ttgo8qnofa.map.gz", "AssetFile": "lib/jquery/dist/jquery.min.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "54456"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ttgo8qnofa"}, {"Name": "integrity", "Value": "sha256-GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map.gz"}]}, {"Route": "lib/jquery/dist/jquery.slim.2z0ns9nrw6.js", "AssetFile": "lib/jquery/dist/jquery.slim.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000014576834"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "68601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=\""}, {"Name": "ETag", "Value": "W/\"UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2z0ns9nrw6"}, {"Name": "integrity", "Value": "sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.js"}]}, {"Route": "lib/jquery/dist/jquery.slim.2z0ns9nrw6.js", "AssetFile": "lib/jquery/dist/jquery.slim.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "232015"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2z0ns9nrw6"}, {"Name": "integrity", "Value": "sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.js"}]}, {"Route": "lib/jquery/dist/jquery.slim.2z0ns9nrw6.js.gz", "AssetFile": "lib/jquery/dist/jquery.slim.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "68601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2z0ns9nrw6"}, {"Name": "integrity", "Value": "sha256-9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.js.gz"}]}, {"Route": "lib/jquery/dist/jquery.slim.js", "AssetFile": "lib/jquery/dist/jquery.slim.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000014576834"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "68601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=\""}, {"Name": "ETag", "Value": "W/\"UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc="}]}, {"Route": "lib/jquery/dist/jquery.slim.js", "AssetFile": "lib/jquery/dist/jquery.slim.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "232015"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc="}]}, {"Route": "lib/jquery/dist/jquery.slim.js.gz", "AssetFile": "lib/jquery/dist/jquery.slim.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "68601"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.87fc7y1x7t.map", "AssetFile": "lib/jquery/dist/jquery.slim.min.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000023188944"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "43123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=\""}, {"Name": "ETag", "Value": "W/\"9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87fc7y1x7t"}, {"Name": "integrity", "Value": "sha256-9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.map"}]}, {"Route": "lib/jquery/dist/jquery.slim.min.87fc7y1x7t.map", "AssetFile": "lib/jquery/dist/jquery.slim.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "107143"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87fc7y1x7t"}, {"Name": "integrity", "Value": "sha256-9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.map"}]}, {"Route": "lib/jquery/dist/jquery.slim.min.87fc7y1x7t.map.gz", "AssetFile": "lib/jquery/dist/jquery.slim.min.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "43123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87fc7y1x7t"}, {"Name": "integrity", "Value": "sha256-bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.map.gz"}]}, {"Route": "lib/jquery/dist/jquery.slim.min.js", "AssetFile": "lib/jquery/dist/jquery.slim.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041049218"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24360"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=\""}, {"Name": "ETag", "Value": "W/\"kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.js", "AssetFile": "lib/jquery/dist/jquery.slim.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "70264"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetFile": "lib/jquery/dist/jquery.slim.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24360"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.map", "AssetFile": "lib/jquery/dist/jquery.slim.min.map.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000023188944"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "43123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=\""}, {"Name": "ETag", "Value": "W/\"9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.map", "AssetFile": "lib/jquery/dist/jquery.slim.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "107143"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetFile": "lib/jquery/dist/jquery.slim.min.map.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "43123"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.muycvpuwrr.js", "AssetFile": "lib/jquery/dist/jquery.slim.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000041049218"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24360"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=\""}, {"Name": "ETag", "Value": "W/\"kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "muycvpuwrr"}, {"Name": "integrity", "Value": "sha256-kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.js"}]}, {"Route": "lib/jquery/dist/jquery.slim.min.muycvpuwrr.js", "AssetFile": "lib/jquery/dist/jquery.slim.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "70264"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:20:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "muycvpuwrr"}, {"Name": "integrity", "Value": "sha256-kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.js"}]}, {"Route": "lib/jquery/dist/jquery.slim.min.muycvpuwrr.js.gz", "AssetFile": "lib/jquery/dist/jquery.slim.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "24360"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 17:23:51 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "muycvpuwrr"}, {"Name": "integrity", "Value": "sha256-h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ="}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.js.gz"}]}]}