/* _content/CoffeeShopWebsite/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-i0k1yi038t] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-i0k1yi038t] {
  color: #0077cc;
}

.btn-primary[b-i0k1yi038t] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-i0k1yi038t], .nav-pills .show > .nav-link[b-i0k1yi038t] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-i0k1yi038t] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-i0k1yi038t] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-i0k1yi038t] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-i0k1yi038t] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-i0k1yi038t] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
