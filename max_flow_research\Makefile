# Makefile cho dự án Max Flow Research
# Tác giả: [Tên sinh viên]

# Compiler và flags
CXX = g++
CXXFLAGS = -std=c++11 -Wall -Wextra -O2
DEBUG_FLAGS = -g -DDEBUG

# Thư mục
SRC_DIR = src
BUILD_DIR = build
BIN_DIR = bin

# Files
SOURCES = $(wildcard $(SRC_DIR)/*.cpp)
OBJECTS = $(SOURCES:$(SRC_DIR)/%.cpp=$(BUILD_DIR)/%.o)
TARGET = $(BIN_DIR)/max_flow

# Phần mặc định
.PHONY: all clean debug run help install

all: $(TARGET)

# Tạo thư mục build và bin nếu chưa có
$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

$(BIN_DIR):
	mkdir -p $(BIN_DIR)

# Biên dịch file object
$(BUILD_DIR)/%.o: $(SRC_DIR)/%.cpp | $(BUILD_DIR)
	$(CXX) $(CXXFLAGS) -c $< -o $@

# Liên kết tạo file thực thi
$(TARGET): $(OBJECTS) | $(BIN_DIR)
	$(CXX) $(CXXFLAGS) $(OBJECTS) -o $@

# Biên dịch với debug
debug: CXXFLAGS += $(DEBUG_FLAGS)
debug: clean $(TARGET)

# Chạy chương trình
run: $(TARGET)
	./$(TARGET)

# Dọn dẹp
clean:
	rm -rf $(BUILD_DIR) $(BIN_DIR)

# Cài đặt (copy vào thư mục hệ thống)
install: $(TARGET)
	cp $(TARGET) /usr/local/bin/max_flow

# Hiển thị trợ giúp
help:
	@echo "Makefile cho dự án Max Flow Research"
	@echo ""
	@echo "Các lệnh có sẵn:"
	@echo "  all     - Biên dịch chương trình (mặc định)"
	@echo "  debug   - Biên dịch với thông tin debug"
	@echo "  run     - Biên dịch và chạy chương trình"
	@echo "  clean   - Xóa các file đã biên dịch"
	@echo "  install - Cài đặt vào hệ thống"
	@echo "  help    - Hiển thị trợ giúp này"
	@echo ""
	@echo "Ví dụ sử dụng:"
	@echo "  make        # Biên dịch"
	@echo "  make run    # Biên dịch và chạy"
	@echo "  make clean  # Dọn dẹp"

# Phụ thuộc
$(BUILD_DIR)/main.o: $(SRC_DIR)/main.cpp $(SRC_DIR)/graph.h
$(BUILD_DIR)/max_flow.o: $(SRC_DIR)/max_flow.cpp $(SRC_DIR)/graph.h
