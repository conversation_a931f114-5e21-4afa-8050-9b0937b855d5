using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using CoffeeShopWebsite.Data;
using CoffeeShopWebsite.Models;

namespace CoffeeShopWebsite.Services
{
    public class DataSeeder
    {
        private readonly CoffeeShopContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;

        public DataSeeder(CoffeeShopContext context, UserManager<ApplicationUser> userManager, RoleManager<IdentityRole> roleManager)
        {
            _context = context;
            _userManager = userManager;
            _roleManager = roleManager;
        }

        public async Task SeedAsync()
        {
            // Ensure database is created
            await _context.Database.EnsureCreatedAsync();

            // Seed roles
            await SeedRolesAsync();

            // Seed categories
            await SeedCategoriesAsync();

            // Seed products
            await SeedProductsAsync();

            // Seed coupons
            await SeedCouponsAsync();

            // Seed admin user
            await SeedAdminUserAsync();
        }

        private async Task SeedRolesAsync()
        {
            if (!await _roleManager.RoleExistsAsync("Admin"))
            {
                await _roleManager.CreateAsync(new IdentityRole("Admin"));
            }

            if (!await _roleManager.RoleExistsAsync("Customer"))
            {
                await _roleManager.CreateAsync(new IdentityRole("Customer"));
            }
        }

        private async Task SeedCategoriesAsync()
        {
            if (!await _context.Categories.AnyAsync())
            {
                var categories = new List<Category>
                {
                    new Category { Name = "Cà phê", Description = "Các loại cà phê truyền thống và hiện đại", IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=300&h=200&fit=crop" },
                    new Category { Name = "Trà", Description = "Các loại trà và trà sữa thơm ngon", IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop" },
                    new Category { Name = "Nước ép", Description = "Nước ép trái cây tươi 100% tự nhiên", IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1613478223719-2ab802602423?w=300&h=200&fit=crop" },
                    new Category { Name = "Sinh tố", Description = "Sinh tố các loại bổ dưỡng", IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1505252585461-04db1eb84625?w=300&h=200&fit=crop" },
                    new Category { Name = "Đá xay", Description = "Các loại đồ uống đá xay mát lạnh", IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1551024506-0bccd828d307?w=300&h=200&fit=crop" }
                };

                await _context.Categories.AddRangeAsync(categories);
                await _context.SaveChangesAsync();
            }
        }

        private async Task SeedProductsAsync()
        {
            if (!await _context.Products.AnyAsync())
            {
                var products = new List<Product>
                {
                    // Cà phê
                    new Product { Name = "Cà phê đen", Description = "Cà phê đen truyền thống, đậm đà hương vị Việt Nam", Price = 15000, StockQuantity = 100, CategoryId = 1, IsActive = true, IsFeatured = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300&h=300&fit=crop" },
                    new Product { Name = "Cà phê sữa", Description = "Cà phê sữa đá thơm ngon, ngọt ngào", Price = 18000, StockQuantity = 100, CategoryId = 1, IsActive = true, IsFeatured = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=300&h=300&fit=crop" },
                    new Product { Name = "Cappuccino", Description = "Cappuccino Ý với lớp foam mịn màng", Price = 35000, StockQuantity = 50, CategoryId = 1, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1572442388796-11668a67e53d?w=300&h=300&fit=crop" },
                    new Product { Name = "Latte", Description = "Latte thơm ngon với nghệ thuật latte art", Price = 40000, StockQuantity = 50, CategoryId = 1, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1561047029-3000c68339ca?w=300&h=300&fit=crop" },
                    new Product { Name = "Espresso", Description = "Espresso đậm đà, tinh túy của cà phê Ý", Price = 25000, StockQuantity = 60, CategoryId = 1, IsActive = true, IsFeatured = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1510707577719-ae7c14805e3a?w=300&h=300&fit=crop" },
                    new Product { Name = "Americano", Description = "Americano nhẹ nhàng, thích hợp mọi lúc", Price = 22000, StockQuantity = 80, CategoryId = 1, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1497935586351-b67a49e012bf?w=300&h=300&fit=crop" },
                    new Product { Name = "Macchiato", Description = "Macchiato với lớp foam đặc trưng", Price = 38000, StockQuantity = 45, CategoryId = 1, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop" },
                    new Product { Name = "Mocha", Description = "Mocha ngọt ngào với chocolate", Price = 42000, StockQuantity = 55, CategoryId = 1, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop" },

                    // Trà
                    new Product { Name = "Trà đào", Description = "Trà đào cam sả tươi mát, thơm ngon", Price = 25000, StockQuantity = 80, CategoryId = 2, IsActive = true, IsFeatured = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=300&h=300&fit=crop" },
                    new Product { Name = "Trà sữa trân châu", Description = "Trà sữa trân châu đường đen đậm đà", Price = 30000, StockQuantity = 60, CategoryId = 2, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1525385133512-2f3bdd039054?w=300&h=300&fit=crop" },
                    new Product { Name = "Trà xanh", Description = "Trà xanh thanh mát, tốt cho sức khỏe", Price = 20000, StockQuantity = 70, CategoryId = 2, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=300&h=300&fit=crop" },
                    new Product { Name = "Trà ô long", Description = "Trà ô long thơm ngon truyền thống", Price = 24000, StockQuantity = 50, CategoryId = 2, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop" },
                    new Product { Name = "Trà sữa matcha", Description = "Trà sữa matcha Nhật Bản", Price = 35000, StockQuantity = 40, CategoryId = 2, IsActive = true, IsFeatured = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1515823064-d6e0c04616a7?w=300&h=300&fit=crop" },
                    new Product { Name = "Trà chanh", Description = "Trà chanh tươi mát giải khát", Price = 18000, StockQuantity = 90, CategoryId = 2, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=300&h=300&fit=crop" },

                    // Nước ép
                    new Product { Name = "Nước ép cam", Description = "Nước ép cam tươi 100% không đường", Price = 20000, StockQuantity = 40, CategoryId = 3, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=300&h=300&fit=crop" },
                    new Product { Name = "Nước ép dưa hấu", Description = "Nước ép dưa hấu tươi mát, giải nhiệt", Price = 22000, StockQuantity = 30, CategoryId = 3, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=300&h=300&fit=crop" },
                    new Product { Name = "Nước ép táo", Description = "Nước ép táo tươi ngọt tự nhiên", Price = 22000, StockQuantity = 35, CategoryId = 3, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=300&h=300&fit=crop" },
                    new Product { Name = "Nước ép cà rót", Description = "Nước ép cà rót giàu vitamin", Price = 25000, StockQuantity = 30, CategoryId = 3, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1622597467836-f3285f2131b8?w=300&h=300&fit=crop" },
                    new Product { Name = "Nước ép dứa", Description = "Nước ép dứa tươi mát nhiệt đới", Price = 23000, StockQuantity = 45, CategoryId = 3, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1589733955941-5eeaf752f6dd?w=300&h=300&fit=crop" },

                    // Sinh tố
                    new Product { Name = "Sinh tố bơ", Description = "Sinh tố bơ béo ngậy, bổ dưỡng", Price = 28000, StockQuantity = 35, CategoryId = 4, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1553530666-ba11a7da3888?w=300&h=300&fit=crop" },
                    new Product { Name = "Sinh tố xoài", Description = "Sinh tố xoài ngọt mát, thơm ngon", Price = 26000, StockQuantity = 40, CategoryId = 4, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1546173159-315724a31696?w=300&h=300&fit=crop" },
                    new Product { Name = "Sinh tố dâu", Description = "Sinh tố dâu tươi ngọt ngào", Price = 30000, StockQuantity = 25, CategoryId = 4, IsActive = true, IsFeatured = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1553530666-ba11a7da3888?w=300&h=300&fit=crop" },
                    new Product { Name = "Sinh tố chuối", Description = "Sinh tố chuối bổ dưỡng", Price = 24000, StockQuantity = 50, CategoryId = 4, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1505252585461-04db1eb84625?w=300&h=300&fit=crop" },
                    new Product { Name = "Sinh tố mix berry", Description = "Sinh tố mix các loại berry", Price = 35000, StockQuantity = 20, CategoryId = 4, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1546173159-315724a31696?w=300&h=300&fit=crop" },

                    // Đá xay
                    new Product { Name = "Frappuccino cà phê", Description = "Frappuccino cà phê đá xay mát lạnh", Price = 45000, StockQuantity = 30, CategoryId = 5, IsActive = true, IsFeatured = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1551024506-0bccd828d307?w=300&h=300&fit=crop" },
                    new Product { Name = "Chocolate đá xay", Description = "Chocolate đá xay ngọt ngào", Price = 40000, StockQuantity = 35, CategoryId = 5, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop" },
                    new Product { Name = "Matcha đá xay", Description = "Matcha đá xay phong cách Nhật", Price = 48000, StockQuantity = 25, CategoryId = 5, IsActive = true, CreatedAt = DateTime.Now, ImageUrl = "https://images.unsplash.com/photo-1515823064-d6e0c04616a7?w=300&h=300&fit=crop" }
                };

                await _context.Products.AddRangeAsync(products);
                await _context.SaveChangesAsync();
            }
        }

        private async Task SeedCouponsAsync()
        {
            if (!await _context.Coupons.AnyAsync())
            {
                var coupons = new List<Coupon>
                {
                    new Coupon 
                    { 
                        Code = "WELCOME10", 
                        Name = "Chào mừng thành viên mới", 
                        Description = "Giảm 10% cho đơn hàng đầu tiên", 
                        Type = CouponType.Percentage, 
                        Value = 10, 
                        MinOrderAmount = 50000,
                        MaxDiscountAmount = 20000,
                        StartDate = DateTime.Now, 
                        EndDate = DateTime.Now.AddYears(1),
                        IsForNewCustomersOnly = true,
                        Status = CouponStatus.Active,
                        CreatedAt = DateTime.Now
                    },
                    new Coupon 
                    { 
                        Code = "FREESHIP", 
                        Name = "Miễn phí vận chuyển", 
                        Description = "Miễn phí vận chuyển cho đơn hàng từ 100k", 
                        Type = CouponType.FreeShipping, 
                        Value = 0, 
                        MinOrderAmount = 100000,
                        StartDate = DateTime.Now, 
                        EndDate = DateTime.Now.AddYears(1),
                        Status = CouponStatus.Active,
                        CreatedAt = DateTime.Now
                    },
                    new Coupon 
                    { 
                        Code = "GOLD20", 
                        Name = "Ưu đãi thành viên Gold", 
                        Description = "Giảm 20% cho thành viên Gold", 
                        Type = CouponType.Percentage, 
                        Value = 20, 
                        MinOrderAmount = 200000,
                        MaxDiscountAmount = 50000,
                        StartDate = DateTime.Now, 
                        EndDate = DateTime.Now.AddYears(1),
                        RequiredCustomerLevel = "Gold",
                        Status = CouponStatus.Active,
                        CreatedAt = DateTime.Now
                    }
                };

                await _context.Coupons.AddRangeAsync(coupons);
                await _context.SaveChangesAsync();
            }
        }

        private async Task SeedAdminUserAsync()
        {
            var adminEmail = "<EMAIL>";
            var adminUser = await _userManager.FindByEmailAsync(adminEmail);

            if (adminUser == null)
            {
                adminUser = new ApplicationUser
                {
                    UserName = adminEmail,
                    Email = adminEmail,
                    FullName = "Administrator",
                    EmailConfirmed = true,
                    CreatedAt = DateTime.Now,
                    LoyaltyPoints = 0,
                    CustomerLevel = "Platinum"
                };

                var result = await _userManager.CreateAsync(adminUser, "Admin123!");
                if (result.Succeeded)
                {
                    await _userManager.AddToRoleAsync(adminUser, "Admin");
                }
            }
        }
    }
}
