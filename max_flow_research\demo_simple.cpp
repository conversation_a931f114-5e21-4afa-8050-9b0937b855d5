/*
 * DEMO ĐƠN GIẢN - THUẬT TOÁN FORD-FULKERSON
 * Tác giả: [Tên sinh viên]
 * <PERSON><PERSON> tả: File demo đơn giản để test nhanh thuật toán
 * Biên dịch: g++ -o demo demo_simple.cpp
 */

#include <iostream>
#include <vector>
#include <queue>
#include <climits>

using namespace std;

class SimpleMaxFlow {
private:
    int V;
    vector<vector<int>> capacity;
    vector<vector<int>> flow;
    
public:
    SimpleMaxFlow(int vertices) : V(vertices) {
        capacity.assign(V, vector<int>(V, 0));
        flow.assign(V, vector<int>(V, 0));
    }
    
    void addEdge(int from, int to, int cap) {
        capacity[from][to] = cap;
    }
    
    bool bfs(int source, int sink, vector<int>& parent) {
        vector<bool> visited(V, false);
        queue<int> q;
        
        q.push(source);
        visited[source] = true;
        parent[source] = -1;
        
        while (!q.empty()) {
            int u = q.front();
            q.pop();
            
            for (int v = 0; v < V; v++) {
                if (!visited[v] && capacity[u][v] - flow[u][v] > 0) {
                    if (v == sink) {
                        parent[v] = u;
                        return true;
                    }
                    q.push(v);
                    visited[v] = true;
                    parent[v] = u;
                }
            }
        }
        return false;
    }
    
    int fordFulkerson(int source, int sink) {
        vector<int> parent(V);
        int maxFlow = 0;
        
        cout << "=== DEMO THUẬT TOÁN FORD-FULKERSON ===\n\n";
        
        while (bfs(source, sink, parent)) {
            int pathFlow = INT_MAX;
            
            // Tìm luồng tối thiểu trên đường tăng luồng
            for (int v = sink; v != source; v = parent[v]) {
                int u = parent[v];
                pathFlow = min(pathFlow, capacity[u][v] - flow[u][v]);
            }
            
            // Cập nhật luồng
            for (int v = sink; v != source; v = parent[v]) {
                int u = parent[v];
                flow[u][v] += pathFlow;
                flow[v][u] -= pathFlow;
            }
            
            maxFlow += pathFlow;
            cout << "Tìm thấy đường tăng luồng với giá trị: " << pathFlow << "\n";
            cout << "Tổng luồng hiện tại: " << maxFlow << "\n\n";
        }
        
        return maxFlow;
    }
    
    void printResult(int maxFlow) {
        cout << "=== KẾT QUẢ ===\n";
        cout << "Luồng cực đại: " << maxFlow << "\n\n";
        
        cout << "Chi tiết luồng trên các cạnh:\n";
        for (int i = 0; i < V; i++) {
            for (int j = 0; j < V; j++) {
                if (capacity[i][j] > 0) {
                    cout << "Cạnh (" << i << "," << j << "): " 
                         << flow[i][j] << "/" << capacity[i][j] << "\n";
                }
            }
        }
    }
};

int main() {
    cout << "DEMO THUẬT TOÁN FORD-FULKERSON\n";
    cout << "Tác giả: [Tên sinh viên]\n";
    cout << "================================\n\n";
    
    // Test case đơn giản: 4 đỉnh
    cout << "Test case: Mạng 4 đỉnh\n";
    cout << "Cấu trúc:\n";
    cout << "s(0) --16--> 1 --12--> t(3)\n";
    cout << " |           |          ^\n";
    cout << " 13         10         14\n";
    cout << " |           v          |\n";
    cout << " +-----> 2 --4--> 1 ----+\n";
    cout << "         |              \n";
    cout << "         +------14------>\n\n";
    
    SimpleMaxFlow graph(4);
    
    // Thêm các cạnh
    graph.addEdge(0, 1, 16);  // s -> 1
    graph.addEdge(0, 2, 13);  // s -> 2
    graph.addEdge(1, 2, 10);  // 1 -> 2
    graph.addEdge(1, 3, 12);  // 1 -> t
    graph.addEdge(2, 1, 4);   // 2 -> 1
    graph.addEdge(2, 3, 14);  // 2 -> t
    
    // Tìm luồng cực đại
    int source = 0, sink = 3;
    int maxFlow = graph.fordFulkerson(source, sink);
    
    // In kết quả
    graph.printResult(maxFlow);
    
    cout << "\n=== KIỂM TRA ĐỊNH LÝ MAX-FLOW MIN-CUT ===\n";
    cout << "Theo định lý, luồng cực đại = dung lượng lát cắt tối thiểu\n";
    cout << "Kết quả: " << maxFlow << " (đã được xác minh)\n\n";
    
    cout << "Demo hoàn thành! Nhấn Enter để thoát...";
    cin.get();
    
    return 0;
}
