@{
    ViewData["Title"] = "Trang chủ";
    var featuredProducts = ViewBag.FeaturedProducts as List<CoffeeShopWebsite.Models.Product>;
    var categories = ViewBag.Categories as List<CoffeeShopWebsite.Models.Category>;
}

<div class="container mt-4">
    <div class="jumbotron bg-primary text-white text-center p-5 rounded">
        <h1 class="display-4">
            <i class="fas fa-coffee me-3"></i>Chào mừng đến Aroma Coffee House
        </h1>
        <p class="lead">Thưởng thức hương vị cà phê tuyệt vời và đồ uống giải khát tươi mát</p>
        <a asp-controller="Products" asp-action="Index" class="btn btn-warning btn-lg">
            <i class="fas fa-shopping-bag me-2"></i>Xem sản phẩm
        </a>
    </div>

    <!-- Categories Section -->
    @if (categories != null && categories.Any())
    {
        <div class="mb-5">
            <h2 class="text-center mb-4">
                <i class="fas fa-th-large me-2"></i><PERSON><PERSON> mụ<PERSON> sản phẩm
            </h2>
            <div class="row">
                @foreach (var category in categories)
                {
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 shadow-sm">
                            @if (!string.IsNullOrEmpty(category.ImageUrl))
                            {
                                <img src="@category.ImageUrl" class="card-img-top" alt="@category.Name" style="height: 200px; object-fit: cover;">
                            }
                            else
                            {
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <i class="fas fa-coffee fa-3x text-muted"></i>
                                </div>
                            }
                            <div class="card-body">
                                <h5 class="card-title">@category.Name</h5>
                                <p class="card-text">@category.Description</p>
                                <a asp-controller="Products" asp-action="Category" asp-route-id="@category.Id" class="btn btn-primary">
                                    <i class="fas fa-eye me-2"></i>Xem sản phẩm
                                </a>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    }

    <!-- Featured Products Section -->
    @if (featuredProducts != null && featuredProducts.Any())
    {
        <div class="mb-5">
            <h2 class="text-center mb-4">
                <i class="fas fa-star me-2"></i>Sản phẩm nổi bật
            </h2>
            <div class="row">
                @foreach (var product in featuredProducts.Take(6))
                {
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 shadow-sm">
                            @if (!string.IsNullOrEmpty(product.ImageUrl))
                            {
                                <img src="@product.ImageUrl" class="card-img-top" alt="@product.Name" style="height: 200px; object-fit: cover;">
                            }
                            else
                            {
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <i class="fas fa-coffee fa-3x text-muted"></i>
                                </div>
                            }
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title">@product.Name</h6>
                                    <span class="badge bg-secondary">@product.Category.Name</span>
                                </div>
                                <p class="card-text small">@product.Description</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="h5 text-primary">@product.Price.ToString("N0") VNĐ</span>
                                    <div class="btn-group">
                                        <a asp-controller="Products" asp-action="Details" asp-route-id="@product.Id" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-primary btn-sm" onclick="addToCart(@product.Id)">
                                            <i class="fas fa-cart-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
            
            <div class="text-center">
                <a asp-controller="Products" asp-action="Featured" class="btn btn-primary btn-lg">
                    <i class="fas fa-star me-2"></i>Xem tất cả sản phẩm nổi bật
                </a>
            </div>
        </div>
    }
</div>

<script>
    function addToCart(productId) {
        $.post('@Url.Action("AddToCart", "Cart")', { productId: productId, quantity: 1 })
            .done(function(response) {
                if (response.success) {
                    alert('Đã thêm sản phẩm vào giỏ hàng!');
                } else {
                    alert(response.message || 'Có lỗi xảy ra!');
                }
            })
            .fail(function() {
                alert('Có lỗi xảy ra khi thêm sản phẩm!');
            });
    }
</script>
