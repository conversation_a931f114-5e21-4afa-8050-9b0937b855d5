@{
    ViewData["Title"] = "Trang chủ";
    var featuredProducts = ViewBag.FeaturedProducts as List<CoffeeShopWebsite.Models.Product>;
    var categories = ViewBag.Categories as List<CoffeeShopWebsite.Models.Category>;
}

<!-- Hero Section -->
<section class="hero-section position-relative overflow-hidden">
    <div class="hero-bg" style="background: linear-gradient(135deg, rgba(139, 69, 19, 0.8), rgba(210, 105, 30, 0.8)), url('https://images.unsplash.com/photo-1447933601403-0c6688de566e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2061&q=80'); background-size: cover; background-position: center; min-height: 80vh; display: flex; align-items: center;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 text-white">
                    <h1 class="display-3 fw-bold mb-4" style="font-family: 'Dancing Script', cursive;">
                        Chào mừng đến với<br>
                        <span class="text-warning">Aroma Coffee House</span>
                    </h1>
                    <p class="lead mb-4 fs-5">
                        Thưởng thức hương vị cà phê đậm đà, được pha chế từ những hạt cà phê chất lượng cao nhất. 
                        Không gian ấm cúng, dịch vụ tận tâm - nơi lý tưởng để thư giãn và làm việc.
                    </p>
                    <div class="d-flex flex-wrap gap-3">
                        <a asp-controller="Products" asp-action="Index" class="btn btn-warning btn-lg rounded-pill px-4">
                            <i class="fas fa-coffee me-2"></i>Khám phá menu
                        </a>
                        <a asp-controller="Products" asp-action="Featured" class="btn btn-outline-light btn-lg rounded-pill px-4">
                            <i class="fas fa-star me-2"></i>Sản phẩm nổi bật
                        </a>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <div class="hero-image-container position-relative">
                        <img src="https://images.unsplash.com/photo-1509042239860-f550ce710b93?ixlib=rb-4.0.3&auto=format&fit=crop&w=687&q=80" 
                             alt="Coffee Cup" class="img-fluid rounded-circle shadow-lg" style="max-width: 400px; animation: float 3s ease-in-out infinite;">
                        <div class="floating-elements">
                            <div class="floating-icon" style="position: absolute; top: 10%; left: 10%; animation: bounce 2s infinite;">
                                <i class="fas fa-coffee text-warning fs-1"></i>
                            </div>
                            <div class="floating-icon" style="position: absolute; bottom: 20%; right: 10%; animation: bounce 2s infinite 0.5s;">
                                <i class="fas fa-heart text-danger fs-2"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold text-dark mb-3">Tại sao chọn chúng tôi?</h2>
            <p class="lead text-muted">Những điều đặc biệt làm nên thương hiệu Aroma Coffee House</p>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="feature-card h-100 p-4 bg-white rounded-3 shadow-sm text-center border-0 hover-lift">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-coffee fa-3x text-warning"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Cà phê chất lượng cao</h5>
                    <p class="text-muted mb-3">
                        Được chọn lọc từ những vùng trồng cà phê nổi tiếng, rang xay theo công thức độc quyền
                    </p>
                    <a asp-controller="Products" asp-action="Index" class="btn btn-outline-warning rounded-pill">
                        Xem menu <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="feature-card h-100 p-4 bg-white rounded-3 shadow-sm text-center border-0 hover-lift">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-shipping-fast fa-3x text-success"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Giao hàng nhanh chóng</h5>
                    <p class="text-muted mb-3">
                        Giao hàng tận nơi trong vòng 30 phút, đảm bảo độ nóng và hương vị tuyệt vời
                    </p>
                    <a asp-controller="Cart" asp-action="Index" class="btn btn-outline-success rounded-pill">
                        Đặt hàng ngay <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="feature-card h-100 p-4 bg-white rounded-3 shadow-sm text-center border-0 hover-lift">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-heart fa-3x text-danger"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Phục vụ tận tâm</h5>
                    <p class="text-muted mb-3">
                        Đội ngũ barista chuyên nghiệp, thân thiện, luôn sẵn sàng tư vấn và phục vụ
                    </p>
                    <a asp-controller="Home" asp-action="Contact" class="btn btn-outline-danger rounded-pill">
                        Liên hệ <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
@if (categories != null && categories.Any())
{
    <section class="categories-section py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold text-dark mb-3">Danh mục sản phẩm</h2>
                <p class="lead text-muted">Khám phá các loại đồ uống đa dạng của chúng tôi</p>
            </div>
            
            <div class="row g-4">
                @foreach (var category in categories)
                {
                    <div class="col-lg-4 col-md-6">
                        <div class="category-card h-100 bg-white rounded-3 shadow-sm overflow-hidden hover-lift">
                            <div class="position-relative">
                                @if (!string.IsNullOrEmpty(category.ImageUrl))
                                {
                                    <img src="@category.ImageUrl" class="card-img-top" alt="@category.Name" style="height: 250px; object-fit: cover;">
                                }
                                else
                                {
                                    <div class="card-img-top bg-gradient d-flex align-items-center justify-content-center" style="height: 250px; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
                                        <i class="fas fa-coffee fa-4x text-white"></i>
                                    </div>
                                }
                                <div class="position-absolute top-0 end-0 m-3">
                                    <span class="badge bg-warning text-dark rounded-pill">Mới</span>
                                </div>
                            </div>
                            <div class="card-body p-4">
                                <h5 class="card-title fw-bold mb-2">@category.Name</h5>
                                <p class="card-text text-muted mb-3">@category.Description</p>
                                <a asp-controller="Products" asp-action="Category" asp-route-id="@category.Id" 
                                   class="btn btn-warning rounded-pill w-100">
                                    <i class="fas fa-eye me-2"></i>Xem sản phẩm
                                </a>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </section>
}

<!-- Featured Products Section -->
@if (featuredProducts != null && featuredProducts.Any())
{
    <section class="featured-products-section py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold text-dark mb-3">Sản phẩm nổi bật</h2>
                <p class="lead text-muted">Những sản phẩm được yêu thích nhất tại Aroma Coffee House</p>
            </div>
            
            <div class="row g-4">
                @foreach (var product in featuredProducts.Take(6))
                {
                    <div class="col-lg-4 col-md-6">
                        <div class="product-card h-100 bg-white rounded-3 shadow-sm overflow-hidden hover-lift">
                            <div class="position-relative">
                                @if (!string.IsNullOrEmpty(product.ImageUrl))
                                {
                                    <img src="@product.ImageUrl" class="card-img-top" alt="@product.Name" style="height: 200px; object-fit: cover;">
                                }
                                else
                                {
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                        <i class="fas fa-coffee fa-3x text-muted"></i>
                                    </div>
                                }
                                @if (product.IsFeatured)
                                {
                                    <div class="position-absolute top-0 start-0 m-3">
                                        <span class="badge bg-danger rounded-pill">
                                            <i class="fas fa-star me-1"></i>Nổi bật
                                        </span>
                                    </div>
                                }
                            </div>
                            <div class="card-body p-4">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title fw-bold mb-0">@product.Name</h6>
                                    <span class="badge bg-secondary">@product.Category.Name</span>
                                </div>
                                <p class="card-text text-muted small mb-3">@product.Description</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="h5 text-warning fw-bold mb-0">@product.Price.ToString("N0") VNĐ</span>
                                    <div class="btn-group">
                                        <a asp-controller="Products" asp-action="Details" asp-route-id="@product.Id" 
                                           class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-warning btn-sm" onclick="addToCart(@product.Id)">
                                            <i class="fas fa-cart-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
            
            <div class="text-center mt-5">
                <a asp-controller="Products" asp-action="Featured" class="btn btn-warning btn-lg rounded-pill px-5">
                    <i class="fas fa-star me-2"></i>Xem tất cả sản phẩm nổi bật
                </a>
            </div>
        </div>
    </section>
}

<!-- CTA Section -->
<section class="cta-section py-5" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h3 class="text-white fw-bold mb-2">Sẵn sàng thưởng thức cà phê tuyệt vời?</h3>
                <p class="text-light mb-0">Đặt hàng ngay để nhận ưu đãi đặc biệt cho khách hàng mới!</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a asp-controller="Products" asp-action="Index" class="btn btn-warning btn-lg rounded-pill px-4 me-3">
                    <i class="fas fa-shopping-cart me-2"></i>Đặt hàng ngay
                </a>
                <a asp-controller="Home" asp-action="Contact" class="btn btn-outline-light btn-lg rounded-pill px-4">
                    <i class="fas fa-phone me-2"></i>Liên hệ
                </a>
            </div>
        </div>
    </div>
</section>

<style>
    @@keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }
    
    @@keyframes bounce {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-10px); }
    }
    
    .hover-lift {
        transition: all 0.3s ease;
    }
    
    .hover-lift:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
    }
    
    .feature-card:hover .feature-icon i {
        transform: scale(1.1);
        transition: all 0.3s ease;
    }
    
    .category-card:hover img {
        transform: scale(1.05);
        transition: all 0.3s ease;
    }
    
    .product-card:hover img {
        transform: scale(1.05);
        transition: all 0.3s ease;
    }
</style>

<script>
    function addToCart(productId) {
        // Add to cart functionality
        $.post('@Url.Action("AddToCart", "Cart")', { productId: productId, quantity: 1 })
            .done(function(response) {
                if (response.success) {
                    updateCartCount();
                    showToast('success', 'Đã thêm sản phẩm vào giỏ hàng!');
                } else {
                    showToast('error', response.message || 'Có lỗi xảy ra!');
                }
            })
            .fail(function() {
                showToast('error', 'Có lỗi xảy ra khi thêm sản phẩm!');
            });
    }
    
    function showToast(type, message) {
        // Simple toast notification
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var toast = $('<div class="alert ' + alertClass + ' alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">' +
                     '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                     message + '</div>');
        
        $('body').append(toast);
        
        setTimeout(function() {
            toast.alert('close');
        }, 3000);
    }
</script>
