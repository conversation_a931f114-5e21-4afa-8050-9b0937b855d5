@model IEnumerable<CoffeeShopWebsite.Models.CartItem>
@{
    ViewData["Title"] = "Giỏ hàng";
    decimal total = Model.Sum(item => item.TotalPrice);
}

<div class="container">
    <h2><i class="fas fa-shopping-cart me-2"></i>Giỏ hàng của bạn</h2>
    
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show">
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            @TempData["ErrorMessage"]
        </div>
    }
    
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show">
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            @TempData["SuccessMessage"]
        </div>
    }

    @if (Model.Any())
    {
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body">
                        @foreach (var item in Model)
                        {
                            <div class="row cart-item mb-3 pb-3 border-bottom" data-cart-item-id="@item.Id">
                                <div class="col-md-2">
                                    @if (!string.IsNullOrEmpty(item.Product.ImageUrl))
                                    {
                                        <img src="@item.Product.ImageUrl" class="img-fluid rounded" alt="@item.Product.Name" style="height: 80px; object-fit: cover;">
                                    }
                                    else
                                    {
                                        <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 80px;">
                                            <i class="fas fa-coffee fa-2x text-muted"></i>
                                        </div>
                                    }
                                </div>
                                <div class="col-md-4">
                                    <h6>@item.Product.Name</h6>
                                    <small class="text-muted">@item.Product.Category.Name</small>
                                    @if (!string.IsNullOrEmpty(item.Notes))
                                    {
                                        <br><small class="text-info"><i class="fas fa-sticky-note me-1"></i>@item.Notes</small>
                                    }
                                </div>
                                <div class="col-md-2">
                                    <span class="price-tag">@item.Product.Price.ToString("N0") VNĐ</span>
                                </div>
                                <div class="col-md-2">
                                    <div class="input-group input-group-sm">
                                        <button class="btn btn-outline-secondary quantity-btn" type="button" data-action="decrease">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" class="form-control text-center quantity-input" 
                                               value="@item.Quantity" min="1" max="@item.Product.StockQuantity">
                                        <button class="btn btn-outline-secondary quantity-btn" type="button" data-action="increase">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <span class="fw-bold item-total">@item.TotalPrice.ToString("N0") VNĐ</span>
                                </div>
                                <div class="col-md-1">
                                    <button class="btn btn-outline-danger btn-sm remove-item" data-cart-item-id="@item.Id">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        }
                        
                        <div class="text-end mt-3">
                            <button class="btn btn-outline-warning me-2" id="clear-cart">
                                <i class="fas fa-trash me-1"></i>Xóa tất cả
                            </button>
                            <a asp-controller="Products" asp-action="Index" class="btn btn-outline-primary">
                                <i class="fas fa-shopping-bag me-1"></i>Tiếp tục mua sắm
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-calculator me-2"></i>Tổng cộng</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tạm tính:</span>
                            <span id="subtotal">@total.ToString("N0") VNĐ</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Phí vận chuyển:</span>
                            <span class="text-success">Miễn phí</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>Tổng cộng:</strong>
                            <strong class="text-primary" id="total">@total.ToString("N0") VNĐ</strong>
                        </div>
                        
                        <a asp-action="Checkout" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-credit-card me-1"></i>Thanh toán
                        </a>
                        
                        <div class="text-center">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>Thanh toán an toàn & bảo mật
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- Promotion Code -->
                <div class="card mt-3">
                    <div class="card-body">
                        <h6><i class="fas fa-tag me-2"></i>Mã giảm giá</h6>
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Nhập mã giảm giá">
                            <button class="btn btn-outline-primary" type="button">
                                Áp dụng
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
            <h4>Giỏ hàng của bạn đang trống</h4>
            <p class="text-muted">Hãy thêm một số sản phẩm vào giỏ hàng để tiếp tục</p>
            <a asp-controller="Products" asp-action="Index" class="btn btn-primary">
                <i class="fas fa-shopping-bag me-1"></i>Bắt đầu mua sắm
            </a>
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Quantity change handlers
            $('.quantity-btn').click(function() {
                var action = $(this).data('action');
                var cartItem = $(this).closest('.cart-item');
                var input = cartItem.find('.quantity-input');
                var currentValue = parseInt(input.val());
                var newValue = action === 'increase' ? currentValue + 1 : currentValue - 1;
                
                if (newValue >= 1 && newValue <= parseInt(input.attr('max'))) {
                    input.val(newValue);
                    updateQuantity(cartItem.data('cart-item-id'), newValue);
                }
            });
            
            $('.quantity-input').change(function() {
                var cartItem = $(this).closest('.cart-item');
                var newValue = parseInt($(this).val());
                
                if (newValue >= 1 && newValue <= parseInt($(this).attr('max'))) {
                    updateQuantity(cartItem.data('cart-item-id'), newValue);
                } else {
                    $(this).val($(this).data('original-value'));
                }
            });
            
            // Remove item
            $('.remove-item').click(function() {
                var cartItemId = $(this).data('cart-item-id');
                var cartItem = $(this).closest('.cart-item');
                
                if (confirm('Bạn có chắc muốn xóa sản phẩm này khỏi giỏ hàng?')) {
                    removeItem(cartItemId, cartItem);
                }
            });
            
            // Clear cart
            $('#clear-cart').click(function() {
                if (confirm('Bạn có chắc muốn xóa tất cả sản phẩm khỏi giỏ hàng?')) {
                    clearCart();
                }
            });
        });
        
        function updateQuantity(cartItemId, quantity) {
            $.ajax({
                url: '@Url.Action("UpdateQuantity", "Cart")',
                type: 'POST',
                data: {
                    cartItemId: cartItemId,
                    quantity: quantity
                },
                success: function(response) {
                    if (response.success) {
                        var cartItem = $('[data-cart-item-id="' + cartItemId + '"]');
                        cartItem.find('.item-total').text(response.itemTotal + ' VNĐ');
                        $('#subtotal').text(response.cartTotal + ' VNĐ');
                        $('#total').text(response.cartTotal + ' VNĐ');
                        updateCartCount();
                    } else {
                        showToast('error', response.message);
                    }
                },
                error: function() {
                    showToast('error', 'Có lỗi xảy ra. Vui lòng thử lại.');
                }
            });
        }
        
        function removeItem(cartItemId, cartItemElement) {
            $.ajax({
                url: '@Url.Action("RemoveItem", "Cart")',
                type: 'POST',
                data: {
                    cartItemId: cartItemId
                },
                success: function(response) {
                    if (response.success) {
                        cartItemElement.fadeOut(300, function() {
                            $(this).remove();
                            if ($('.cart-item').length === 0) {
                                location.reload();
                            }
                        });
                        $('#subtotal').text(response.cartTotal + ' VNĐ');
                        $('#total').text(response.cartTotal + ' VNĐ');
                        updateCartCount();
                        showToast('success', response.message);
                    } else {
                        showToast('error', response.message);
                    }
                },
                error: function() {
                    showToast('error', 'Có lỗi xảy ra. Vui lòng thử lại.');
                }
            });
        }
        
        function clearCart() {
            $.ajax({
                url: '@Url.Action("ClearCart", "Cart")',
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        showToast('error', 'Có lỗi xảy ra. Vui lòng thử lại.');
                    }
                },
                error: function() {
                    showToast('error', 'Có lỗi xảy ra. Vui lòng thử lại.');
                }
            });
        }
        
        function updateCartCount() {
            // Update cart count in navbar
        }
        
        function showToast(type, message) {
            var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            var toast = $('<div class="alert ' + alertClass + ' alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">' +
                         '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                         message + '</div>');
            
            $('body').append(toast);
            
            setTimeout(function() {
                toast.alert('close');
            }, 3000);
        }
    </script>
}
