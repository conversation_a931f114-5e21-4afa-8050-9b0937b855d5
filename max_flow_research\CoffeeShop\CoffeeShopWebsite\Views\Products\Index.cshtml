@model IEnumerable<CoffeeShopWebsite.Models.Product>
@{
    ViewData["Title"] = ViewBag.PageTitle ?? "Sản phẩm";
    var categories = ViewBag.Categories as List<CoffeeShopWebsite.Models.Category>;
}

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Filters -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-filter me-2"></i>Bộ lọc</h5>
                </div>
                <div class="card-body">
                    <!-- Search Form -->
                    <form method="get" class="mb-3">
                        <div class="input-group">
                            <input type="text" name="searchString" value="@ViewBag.CurrentFilter" 
                                   class="form-control" placeholder="Tìm kiếm sản phẩm...">
                            <button class="btn btn-outline-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <input type="hidden" name="categoryId" value="@ViewBag.CurrentCategory" />
                        <input type="hidden" name="sortOrder" value="@ViewBag.CurrentSort" />
                    </form>

                    <!-- Category Filter -->
                    @if (categories != null && categories.Any())
                    {
                        <h6>Danh mục</h6>
                        <div class="list-group list-group-flush">
                            <a href="@Url.Action("Index", new { searchString = ViewBag.CurrentFilter, sortOrder = ViewBag.CurrentSort })" 
                               class="list-group-item list-group-item-action @(ViewBag.CurrentCategory == null ? "active" : "")">
                                <i class="fas fa-th-large me-2"></i>Tất cả
                            </a>
                            @foreach (var category in categories)
                            {
                                <a href="@Url.Action("Index", new { categoryId = category.Id, searchString = ViewBag.CurrentFilter, sortOrder = ViewBag.CurrentSort })" 
                                   class="list-group-item list-group-item-action @(ViewBag.CurrentCategory == category.Id ? "active" : "")">
                                    <i class="fas fa-coffee me-2"></i>@category.Name
                                </a>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="col-md-9">
            <!-- Header with Sort Options -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h2>
                    @if (!string.IsNullOrEmpty(ViewBag.CategoryName))
                    {
                        <span>@ViewBag.CategoryName</span>
                    }
                    else if (!string.IsNullOrEmpty(ViewBag.PageTitle))
                    {
                        <span>@ViewBag.PageTitle</span>
                    }
                    else
                    {
                        <span>Tất cả sản phẩm</span>
                    }
                    <small class="text-muted">(@Model.Count() sản phẩm)</small>
                </h2>
                
                <!-- Sort Options -->
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-sort me-1"></i>Sắp xếp
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="@Url.Action("Index", new { categoryId = ViewBag.CurrentCategory, searchString = ViewBag.CurrentFilter, sortOrder = "" })">
                            Tên A-Z</a></li>
                        <li><a class="dropdown-item" href="@Url.Action("Index", new { categoryId = ViewBag.CurrentCategory, searchString = ViewBag.CurrentFilter, sortOrder = "name_desc" })">
                            Tên Z-A</a></li>
                        <li><a class="dropdown-item" href="@Url.Action("Index", new { categoryId = ViewBag.CurrentCategory, searchString = ViewBag.CurrentFilter, sortOrder = "Price" })">
                            Giá thấp đến cao</a></li>
                        <li><a class="dropdown-item" href="@Url.Action("Index", new { categoryId = ViewBag.CurrentCategory, searchString = ViewBag.CurrentFilter, sortOrder = "price_desc" })">
                            Giá cao đến thấp</a></li>
                    </ul>
                </div>
            </div>

            @if (!string.IsNullOrEmpty(ViewBag.CategoryDescription))
            {
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>@ViewBag.CategoryDescription
                </div>
            }

            <!-- Products Grid -->
            @if (Model.Any())
            {
                <div class="row">
                    @foreach (var product in Model)
                    {
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 shadow-sm product-card">
                                @if (!string.IsNullOrEmpty(product.ImageUrl))
                                {
                                    <div style="height: 200px; overflow: hidden;">
                                        <img src="@product.ImageUrl" class="card-img-top" alt="@product.Name" 
                                             style="height: 200px; object-fit: cover; width: 100%;">
                                    </div>
                                }
                                else
                                {
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                        <i class="fas fa-coffee fa-3x text-muted"></i>
                                    </div>
                                }
                                
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title">@product.Name</h5>
                                    <p class="card-text flex-grow-1">@product.Description</p>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-tag me-1"></i>@product.Category.Name
                                        </small>
                                        @if (product.IsFeatured)
                                        {
                                            <span class="badge bg-warning text-dark ms-2">
                                                <i class="fas fa-star me-1"></i>Nổi bật
                                            </span>
                                        }
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="h5 price-tag mb-0">@product.Price.ToString("N0") VNĐ</span>
                                        <div>
                                            <a asp-action="Details" asp-route-id="@product.Id" 
                                               class="btn btn-outline-primary btn-sm me-1" title="Xem chi tiết">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if (product.StockQuantity > 0)
                                            {
                                                <button class="btn btn-primary btn-sm add-to-cart" 
                                                        data-product-id="@product.Id" title="Thêm vào giỏ hàng">
                                                    <i class="fas fa-cart-plus"></i>
                                                </button>
                                            }
                                            else
                                            {
                                                <button class="btn btn-secondary btn-sm" disabled title="Hết hàng">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            }
                                        </div>
                                    </div>
                                    @if (product.StockQuantity <= 5 && product.StockQuantity > 0)
                                    {
                                        <small class="text-warning mt-1">
                                            <i class="fas fa-exclamation-triangle me-1"></i>Chỉ còn @product.StockQuantity sản phẩm
                                        </small>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4>Không tìm thấy sản phẩm nào</h4>
                    <p class="text-muted">Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm</p>
                    <a asp-action="Index" class="btn btn-primary">
                        <i class="fas fa-refresh me-1"></i>Xem tất cả sản phẩm
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Add to cart functionality
            $('.add-to-cart').click(function() {
                var productId = $(this).data('product-id');
                var button = $(this);
                
                // Disable button during request
                button.prop('disabled', true);
                var originalHtml = button.html();
                button.html('<i class="fas fa-spinner fa-spin"></i>');
                
                $.ajax({
                    url: '@Url.Action("AddToCart", "Products")',
                    type: 'POST',
                    data: {
                        productId: productId,
                        quantity: 1
                    },
                    success: function(response) {
                        if (response.success) {
                            showToast('success', response.message);
                            updateCartCount();
                        } else {
                            showToast('error', response.message);
                        }
                    },
                    error: function() {
                        showToast('error', 'Có lỗi xảy ra. Vui lòng thử lại.');
                    },
                    complete: function() {
                        button.prop('disabled', false);
                        button.html(originalHtml);
                    }
                });
            });
        });
        
        function updateCartCount() {
            // Update cart count in navbar
        }
        
        function showToast(type, message) {
            var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            var toast = $('<div class="alert ' + alertClass + ' alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">' +
                         '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                         message + '</div>');
            
            $('body').append(toast);
            
            setTimeout(function() {
                toast.alert('close');
            }, 3000);
        }
    </script>
}
