using Microsoft.AspNetCore.Mvc;
using CoffeeShopWebsite.Services;

namespace CoffeeShopWebsite.Controllers
{
    public class SeedController : Controller
    {
        private readonly DataSeeder _seeder;

        public SeedController(DataSeeder seeder)
        {
            _seeder = seeder;
        }

        public async Task<IActionResult> Index()
        {
            await _seeder.SeedAsync();
            return Content("Data seeded successfully! <a href='/'>Go to Home</a>", "text/html");
        }
    }
}
