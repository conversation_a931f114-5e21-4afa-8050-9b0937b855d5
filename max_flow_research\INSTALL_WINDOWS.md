# HƯỚNG DẪN CÀI ĐẶT VÀ CHẠY TRÊN WINDOWS

## 🎯 Yêu cầu hệ thống

- **Hệ điều hành:** Windows 7/8/10/11
- **RAM:** Tối thiểu 512MB
- **Dung lượng:** ~50MB (bao gồm compiler)
- **Compiler:** g++ (MinGW) hoặc Visual Studio

## 📥 Cài đặt Compiler

### Phương án 1: Sử dụng Dev-C++ (Khuyến nghị cho sinh viên)

1. **Tải Dev-C++:**
   - Truy cập: https://www.bloodshed.net/devcpp.html
   - Hoặc: https://sourceforge.net/projects/orwelldevcpp/
   - Tải phiên bản mới nhất (khoảng 50MB)

2. **Cài đặt:**
   - Chạy file .exe đã tải
   - Chọn "English" hoặc "Tiếng Việt"
   - Nhấn "Next" → "I Agree" → "Next" → "Install"
   - Chờ cài đặt hoàn tất

3. **Kiểm tra:**
   - Mở Command Prompt (cmd)
   - Gõ: `g++ --version`
   - Nếu hiển thị thông tin version → Thành công

### Phương án 2: Sử dụng MinGW trực tiếp

1. **Tải MinGW:**
   - Truy cập: https://www.mingw-w64.org/downloads/
   - Chọn "MingW-W64-builds"
   - Tải file installer

2. **Cài đặt:**
   - Chạy installer
   - Chọn cấu hình mặc định
   - Chờ tải và cài đặt

3. **Thêm vào PATH:**
   - Mở "System Properties" → "Environment Variables"
   - Thêm đường dẫn MinGW/bin vào PATH
   - Ví dụ: `C:\MinGW\bin`

### Phương án 3: Visual Studio Community (Cho người dùng nâng cao)

1. **Tải Visual Studio:**
   - Truy cập: https://visualstudio.microsoft.com/vs/community/
   - Tải Visual Studio Community (miễn phí)

2. **Cài đặt:**
   - Chọn "Desktop development with C++"
   - Cài đặt các component cần thiết

## 🚀 Chạy chương trình

### Cách 1: Sử dụng script tự động (Khuyến nghị)

1. **Mở Command Prompt:**
   - Nhấn `Win + R`
   - Gõ `cmd` và nhấn Enter

2. **Di chuyển đến thư mục dự án:**
   ```cmd
   cd "đường_dẫn_đến_thư_mục\max_flow_research"
   ```

3. **Chạy script build và run:**
   ```cmd
   run.bat
   ```

### Cách 2: Build thủ công

1. **Build chương trình:**
   ```cmd
   build.bat
   ```

2. **Chạy chương trình:**
   ```cmd
   bin\max_flow.exe
   ```

### Cách 3: Sử dụng Dev-C++

1. **Mở Dev-C++**
2. **Tạo project mới:**
   - File → New → Project
   - Chọn "Console Application"
   - Chọn "C++"

3. **Thêm files:**
   - Project → Add to Project
   - Thêm tất cả file .cpp và .h từ thư mục src/

4. **Build và Run:**
   - Nhấn F9 (Compile & Run)

## 🔧 Xử lý lỗi thường gặp

### Lỗi 1: 'g++' is not recognized
**Nguyên nhân:** Chưa cài đặt compiler hoặc chưa thêm vào PATH

**Giải pháp:**
- Cài đặt Dev-C++ hoặc MinGW
- Kiểm tra PATH environment variable
- Restart Command Prompt sau khi cài đặt

### Lỗi 2: Permission denied
**Nguyên nhân:** Không có quyền ghi file

**Giải pháp:**
- Chạy Command Prompt as Administrator
- Hoặc copy dự án ra Desktop/Documents

### Lỗi 3: File not found
**Nguyên nhân:** Đường dẫn file không đúng

**Giải pháp:**
- Kiểm tra cấu trúc thư mục
- Đảm bảo tất cả file .cpp và .h có trong thư mục src/

### Lỗi 4: Compilation error
**Nguyên nhân:** Lỗi syntax hoặc compiler không hỗ trợ C++11

**Giải pháp:**
- Cập nhật compiler lên phiên bản mới
- Kiểm tra flag `-std=c++11`

## 📁 Cấu trúc thư mục sau khi build

```
max_flow_research/
├── src/                    # Mã nguồn
├── build/                  # File object (tự động tạo)
│   ├── main.o
│   └── max_flow.o
├── bin/                    # File thực thi (tự động tạo)
│   └── max_flow.exe
├── build.bat              # Script build
├── run.bat                # Script chạy
└── ...
```

## 🎮 Sử dụng chương trình

1. **Chạy chương trình:**
   ```cmd
   bin\max_flow.exe
   ```

2. **Chọn menu:**
   - `1`: Test case đơn giản
   - `2`: Test case phức tạp
   - `3`: Test case tùy chỉnh
   - `4`: Nhập mạng mới
   - `5`: Hướng dẫn
   - `0`: Thoát

3. **Xem kết quả:**
   - Quá trình tìm đường tăng luồng
   - Ma trận luồng kết quả
   - Lát cắt tối thiểu

## 💡 Mẹo sử dụng

- **Backup:** Sao lưu thư mục dự án trước khi chỉnh sửa
- **Test:** Chạy các test case có sẵn trước khi nhập dữ liệu mới
- **Debug:** Sử dụng `build.bat` để kiểm tra lỗi biên dịch
- **Performance:** Với đồ thị lớn, chương trình có thể chạy chậm

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra lại hướng dẫn cài đặt
2. Đảm bảo compiler được cài đặt đúng
3. Kiểm tra cấu trúc thư mục
4. Liên hệ: [<EMAIL>]

---

**Chúc bạn thành công với dự án!** 🎉
