using System.ComponentModel.DataAnnotations;

namespace CoffeeShopWebsite.Models
{
    public class UserCoupon
    {
        public int Id { get; set; }
        
        [Required]
        public string UserId { get; set; } = string.Empty;
        
        [Required]
        public int CouponId { get; set; }
        
        public DateTime AssignedAt { get; set; } = DateTime.Now;
        
        public DateTime? UsedAt { get; set; }
        
        public bool IsUsed { get; set; } = false;
        
        public int? OrderId { get; set; }
        
        // Navigation properties
        public virtual ApplicationUser User { get; set; } = null!;
        public virtual Coupon Coupon { get; set; } = null!;
        public virtual Order? Order { get; set; }
    }
}
