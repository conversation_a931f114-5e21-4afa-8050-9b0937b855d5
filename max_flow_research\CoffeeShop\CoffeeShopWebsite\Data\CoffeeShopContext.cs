using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using CoffeeShopWebsite.Models;

namespace CoffeeShopWebsite.Data
{
    public class CoffeeShopContext : IdentityDbContext<ApplicationUser>
    {
        public CoffeeShopContext(DbContextOptions<CoffeeShopContext> options) : base(options)
        {
        }

        public DbSet<Category> Categories { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderItem> OrderItems { get; set; }
        public DbSet<CartItem> CartItems { get; set; }
        public DbSet<Coupon> Coupons { get; set; }
        public DbSet<UserCoupon> UserCoupons { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Category configuration
            modelBuilder.Entity<Category>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.HasIndex(e => e.Name).IsUnique();
            });

            // Product configuration
            modelBuilder.Entity<Product>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Price).HasColumnType("decimal(18,2)");
                
                entity.HasOne(p => p.Category)
                      .WithMany(c => c.Products)
                      .HasForeignKey(p => p.CategoryId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Customer configuration
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FullName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(150);
                entity.Property(e => e.PhoneNumber).IsRequired().HasMaxLength(15);
                entity.Property(e => e.Address).HasMaxLength(300);
                entity.HasIndex(e => e.Email).IsUnique();
            });

            // Order configuration
            modelBuilder.Entity<Order>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OrderNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.CustomerName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.CustomerPhone).IsRequired().HasMaxLength(15);
                entity.Property(e => e.DeliveryAddress).HasMaxLength(300);
                entity.Property(e => e.Notes).HasMaxLength(500);
                entity.HasIndex(e => e.OrderNumber).IsUnique();
                
                entity.HasOne(o => o.Customer)
                      .WithMany(c => c.Orders)
                      .HasForeignKey(o => o.CustomerId)
                      .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(o => o.User)
                      .WithMany(u => u.Orders)
                      .HasForeignKey(o => o.UserId)
                      .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(o => o.Coupon)
                      .WithMany(c => c.Orders)
                      .HasForeignKey(o => o.CouponId)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // OrderItem configuration
            modelBuilder.Entity<OrderItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.UnitPrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Notes).HasMaxLength(200);
                
                entity.HasOne(oi => oi.Order)
                      .WithMany(o => o.OrderItems)
                      .HasForeignKey(oi => oi.OrderId)
                      .OnDelete(DeleteBehavior.Cascade);
                      
                entity.HasOne(oi => oi.Product)
                      .WithMany(p => p.OrderItems)
                      .HasForeignKey(oi => oi.ProductId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // CartItem configuration
            modelBuilder.Entity<CartItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Notes).HasMaxLength(200);
                entity.Property(e => e.SessionId).HasMaxLength(100);
                
                entity.HasOne(ci => ci.Customer)
                      .WithMany(c => c.CartItems)
                      .HasForeignKey(ci => ci.CustomerId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(ci => ci.User)
                      .WithMany(u => u.CartItems)
                      .HasForeignKey(ci => ci.UserId)
                      .OnDelete(DeleteBehavior.Cascade);
                      
                entity.HasOne(ci => ci.Product)
                      .WithMany(p => p.CartItems)
                      .HasForeignKey(ci => ci.ProductId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Coupon configuration
            modelBuilder.Entity<Coupon>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Value).HasColumnType("decimal(18,2)");
                entity.Property(e => e.MinOrderAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.MaxDiscountAmount).HasColumnType("decimal(18,2)");
                entity.HasIndex(e => e.Code).IsUnique();
            });

            // UserCoupon configuration
            modelBuilder.Entity<UserCoupon>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.UserId).IsRequired();

                entity.HasOne(uc => uc.User)
                      .WithMany(u => u.UserCoupons)
                      .HasForeignKey(uc => uc.UserId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(uc => uc.Coupon)
                      .WithMany(c => c.UserCoupons)
                      .HasForeignKey(uc => uc.CouponId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(uc => uc.Order)
                      .WithMany()
                      .HasForeignKey(uc => uc.OrderId)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // Seed data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed Roles
            modelBuilder.Entity<IdentityRole>().HasData(
                new IdentityRole { Id = "1", Name = "Admin", NormalizedName = "ADMIN" },
                new IdentityRole { Id = "2", Name = "Customer", NormalizedName = "CUSTOMER" }
            );

            // Seed Coupons
            modelBuilder.Entity<Coupon>().HasData(
                new Coupon
                {
                    Id = 1,
                    Code = "WELCOME10",
                    Name = "Chào mừng thành viên mới",
                    Description = "Giảm 10% cho đơn hàng đầu tiên",
                    Type = CouponType.Percentage,
                    Value = 10,
                    MinOrderAmount = 50000,
                    MaxDiscountAmount = 20000,
                    StartDate = new DateTime(2024, 1, 1),
                    EndDate = new DateTime(2025, 12, 31),
                    IsForNewCustomersOnly = true,
                    Status = CouponStatus.Active,
                    CreatedAt = new DateTime(2024, 1, 1)
                },
                new Coupon
                {
                    Id = 2,
                    Code = "FREESHIP",
                    Name = "Miễn phí vận chuyển",
                    Description = "Miễn phí vận chuyển cho đơn hàng từ 100k",
                    Type = CouponType.FreeShipping,
                    Value = 0,
                    MinOrderAmount = 100000,
                    StartDate = new DateTime(2024, 1, 1),
                    EndDate = new DateTime(2025, 12, 31),
                    Status = CouponStatus.Active,
                    CreatedAt = new DateTime(2024, 1, 1)
                },
                new Coupon
                {
                    Id = 3,
                    Code = "GOLD20",
                    Name = "Ưu đãi thành viên Gold",
                    Description = "Giảm 20% cho thành viên Gold",
                    Type = CouponType.Percentage,
                    Value = 20,
                    MinOrderAmount = 200000,
                    MaxDiscountAmount = 50000,
                    StartDate = new DateTime(2024, 1, 1),
                    EndDate = new DateTime(2025, 12, 31),
                    RequiredCustomerLevel = "Gold",
                    Status = CouponStatus.Active,
                    CreatedAt = new DateTime(2024, 1, 1)
                }
            );
        }

        private void SeedProductData(ModelBuilder modelBuilder)
        {
            // Seed Categories
            modelBuilder.Entity<Category>().HasData(
                new Category { Id = 1, Name = "Cà phê", Description = "Các loại cà phê truyền thống và hiện đại", IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=300&h=200&fit=crop" },
                new Category { Id = 2, Name = "Trà", Description = "Các loại trà và trà sữa thơm ngon", IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop" },
                new Category { Id = 3, Name = "Nước ép", Description = "Nước ép trái cây tươi 100% tự nhiên", IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1613478223719-2ab802602423?w=300&h=200&fit=crop" },
                new Category { Id = 4, Name = "Sinh tố", Description = "Sinh tố các loại bổ dưỡng", IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1505252585461-04db1eb84625?w=300&h=200&fit=crop" },
                new Category { Id = 5, Name = "Đá xay", Description = "Các loại đồ uống đá xay mát lạnh", IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1551024506-0bccd828d307?w=300&h=200&fit=crop" }
            );

            // Seed Products
            modelBuilder.Entity<Product>().HasData(
                // Cà phê
                new Product { Id = 1, Name = "Cà phê đen", Description = "Cà phê đen truyền thống, đậm đà hương vị Việt Nam", Price = 15000, StockQuantity = 100, CategoryId = 1, IsActive = true, IsFeatured = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300&h=300&fit=crop" },
                new Product { Id = 2, Name = "Cà phê sữa", Description = "Cà phê sữa đá thơm ngon, ngọt ngào", Price = 18000, StockQuantity = 100, CategoryId = 1, IsActive = true, IsFeatured = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=300&h=300&fit=crop" },
                new Product { Id = 3, Name = "Cappuccino", Description = "Cappuccino Ý với lớp foam mịn màng", Price = 35000, StockQuantity = 50, CategoryId = 1, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1572442388796-11668a67e53d?w=300&h=300&fit=crop" },
                new Product { Id = 4, Name = "Latte", Description = "Latte thơm ngon với nghệ thuật latte art", Price = 40000, StockQuantity = 50, CategoryId = 1, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1561047029-3000c68339ca?w=300&h=300&fit=crop" },

                // Trà
                new Product { Id = 5, Name = "Trà đào", Description = "Trà đào cam sả tươi mát, thơm ngon", Price = 25000, StockQuantity = 80, CategoryId = 2, IsActive = true, IsFeatured = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=300&h=300&fit=crop" },
                new Product { Id = 6, Name = "Trà sữa trân châu", Description = "Trà sữa trân châu đường đen đậm đà", Price = 30000, StockQuantity = 60, CategoryId = 2, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1525385133512-2f3bdd039054?w=300&h=300&fit=crop" },

                // Nước ép
                new Product { Id = 7, Name = "Nước ép cam", Description = "Nước ép cam tươi 100% không đường", Price = 20000, StockQuantity = 40, CategoryId = 3, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=300&h=300&fit=crop" },
                new Product { Id = 8, Name = "Nước ép dưa hấu", Description = "Nước ép dưa hấu tươi mát, giải nhiệt", Price = 22000, StockQuantity = 30, CategoryId = 3, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=300&h=300&fit=crop" },

                // Sinh tố
                new Product { Id = 9, Name = "Sinh tố bơ", Description = "Sinh tố bơ béo ngậy, bổ dưỡng", Price = 28000, StockQuantity = 35, CategoryId = 4, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1553530666-ba11a7da3888?w=300&h=300&fit=crop" },
                new Product { Id = 10, Name = "Sinh tố xoài", Description = "Sinh tố xoài ngọt mát, thơm ngon", Price = 26000, StockQuantity = 40, CategoryId = 4, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1546173159-315724a31696?w=300&h=300&fit=crop" },

                // Thêm sản phẩm cà phê
                new Product { Id = 11, Name = "Espresso", Description = "Espresso đậm đà, tinh túy của cà phê Ý", Price = 25000, StockQuantity = 60, CategoryId = 1, IsActive = true, IsFeatured = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1510707577719-ae7c14805e3a?w=300&h=300&fit=crop" },
                new Product { Id = 12, Name = "Americano", Description = "Americano nhẹ nhàng, thích hợp mọi lúc", Price = 22000, StockQuantity = 80, CategoryId = 1, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1497935586351-b67a49e012bf?w=300&h=300&fit=crop" },
                new Product { Id = 13, Name = "Macchiato", Description = "Macchiato với lớp foam đặc trưng", Price = 38000, StockQuantity = 45, CategoryId = 1, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop" },
                new Product { Id = 14, Name = "Mocha", Description = "Mocha ngọt ngào với chocolate", Price = 42000, StockQuantity = 55, CategoryId = 1, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop" },

                // Thêm sản phẩm trà
                new Product { Id = 15, Name = "Trà xanh", Description = "Trà xanh thanh mát, tốt cho sức khỏe", Price = 20000, StockQuantity = 70, CategoryId = 2, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=300&h=300&fit=crop" },
                new Product { Id = 16, Name = "Trà ô long", Description = "Trà ô long thơm ngon truyền thống", Price = 24000, StockQuantity = 50, CategoryId = 2, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop" },
                new Product { Id = 17, Name = "Trà sữa matcha", Description = "Trà sữa matcha Nhật Bản", Price = 35000, StockQuantity = 40, CategoryId = 2, IsActive = true, IsFeatured = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1515823064-d6e0c04616a7?w=300&h=300&fit=crop" },
                new Product { Id = 18, Name = "Trà chanh", Description = "Trà chanh tươi mát giải khát", Price = 18000, StockQuantity = 90, CategoryId = 2, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=300&h=300&fit=crop" },

                // Thêm sản phẩm nước ép
                new Product { Id = 19, Name = "Nước ép táo", Description = "Nước ép táo tươi ngọt tự nhiên", Price = 22000, StockQuantity = 35, CategoryId = 3, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=300&h=300&fit=crop" },
                new Product { Id = 20, Name = "Nước ép cà rót", Description = "Nước ép cà rót giàu vitamin", Price = 25000, StockQuantity = 30, CategoryId = 3, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1622597467836-f3285f2131b8?w=300&h=300&fit=crop" },
                new Product { Id = 21, Name = "Nước ép dứa", Description = "Nước ép dứa tươi mát nhiệt đới", Price = 23000, StockQuantity = 45, CategoryId = 3, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1589733955941-5eeaf752f6dd?w=300&h=300&fit=crop" },

                // Thêm sản phẩm sinh tố
                new Product { Id = 22, Name = "Sinh tố dâu", Description = "Sinh tố dâu tươi ngọt ngào", Price = 30000, StockQuantity = 25, CategoryId = 4, IsActive = true, IsFeatured = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1553530666-ba11a7da3888?w=300&h=300&fit=crop" },
                new Product { Id = 23, Name = "Sinh tố chuối", Description = "Sinh tố chuối bổ dưỡng", Price = 24000, StockQuantity = 50, CategoryId = 4, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1505252585461-04db1eb84625?w=300&h=300&fit=crop" },
                new Product { Id = 24, Name = "Sinh tố mix berry", Description = "Sinh tố mix các loại berry", Price = 35000, StockQuantity = 20, CategoryId = 4, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1546173159-315724a31696?w=300&h=300&fit=crop" },

                // Thêm sản phẩm đá xay
                new Product { Id = 25, Name = "Frappuccino cà phê", Description = "Frappuccino cà phê đá xay mát lạnh", Price = 45000, StockQuantity = 30, CategoryId = 5, IsActive = true, IsFeatured = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1551024506-0bccd828d307?w=300&h=300&fit=crop" },
                new Product { Id = 26, Name = "Chocolate đá xay", Description = "Chocolate đá xay ngọt ngào", Price = 40000, StockQuantity = 35, CategoryId = 5, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=300&fit=crop" },
                new Product { Id = 27, Name = "Matcha đá xay", Description = "Matcha đá xay phong cách Nhật", Price = 48000, StockQuantity = 25, CategoryId = 5, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1515823064-d6e0c04616a7?w=300&h=300&fit=crop" }
            );
        }


    }
}
